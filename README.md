# 论文自动化处理系统

基于 GitHub Actions 的论文自动化处理系统，能够自动爬取最新论文、生成AI摘要、渲染网页并推送邮件通知。

## 🎯 项目目标

开发一个完全自动化的论文处理流水线，帮助研究人员及时获取和了解最新的学术动态。

## 🏗️ 系统架构

```
paper-automation-system/
├── src/
│   ├── crawler/          # 论文爬取模块
│   ├── summarizer/       # AI摘要生成模块
│   ├── renderer/         # 网页渲染模块
│   ├── mailer/          # 邮件推送模块
│   └── utils/           # 通用工具模块
├── .github/
│   └── workflows/       # GitHub Actions 工作流
├── config/              # 配置文件
├── templates/           # 网页模板
├── output/              # 输出文件
└── docs/               # 项目文档
```

## 🚀 核心功能

### 1. 论文爬取模块
- 支持多个学术网站（arXiv、PubMed、IEEE等）
- 可配置的爬取规则和关键词过滤
- 增量更新，避免重复爬取

### 2. AI摘要生成模块
- 集成多个大语言模型API（OpenAI、Claude、本地模型等）
- 智能摘要生成和关键信息提取
- 支持批量处理和错误重试

### 3. 网页渲染模块
- 响应式网页设计
- 论文分类和搜索功能
- 静态网站生成，支持GitHub Pages

### 4. 邮件推送模块
- 定制化邮件模板
- 支持多种邮件服务提供商
- 智能推送策略（摘要、全文、链接等）

## 📋 开发任务清单

### Phase 1: 基础框架搭建 ✅
- [x] 项目结构初始化
- [x] README.md 编写
- [x] CHANGELOG.md 创建
- [x] 基础配置文件设置
- [x] 依赖管理配置

### Phase 2: 论文爬取模块 ✅
- [x] arXiv API 集成
- [ ] PubMed API 集成
- [x] 通用爬虫框架
- [x] 数据存储和去重
- [x] 配置化爬取规则

### Phase 3: AI摘要生成模块 ✅
- [x] OpenAI API 集成
- [x] Claude API 集成
- [ ] 本地模型支持
- [ ] 摘要质量评估
- [x] 批量处理优化

### Phase 4: 网页渲染模块 ✅
- [x] 静态网站生成器
- [x] 响应式模板设计
- [x] 搜索和过滤功能
- [x] GitHub Pages 集成

### Phase 5: 邮件推送模块 ✅
- [x] SMTP 配置
- [x] 邮件模板设计
- [x] 推送策略配置
- [ ] 订阅管理系统

### Phase 6: GitHub Actions 集成 ✅
- [x] 定时触发工作流
- [x] 环境变量配置
- [x] 错误处理和通知
- [x] 部署自动化

### Phase 7: 测试和优化 🚧
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [x] 文档完善

## 🛠️ 技术栈

- **语言**: Python 3.9+
- **爬虫**: requests, BeautifulSoup4, scrapy
- **AI集成**: openai, anthropic, transformers
- **网页生成**: Jinja2, Bootstrap
- **邮件**: smtplib, email
- **自动化**: GitHub Actions
- **数据存储**: JSON, SQLite
- **部署**: GitHub Pages

## 📦 安装和使用

### 环境要求
- Python 3.9 或更高版本
- Git
- GitHub 账户

### 快速开始

#### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd paper-automation-system

# 创建并激活conda环境
conda create -n paper-automation python=3.9
conda activate paper-automation

# 安装依赖
pip install -r requirements.txt
```

#### 2. 配置API密钥
**方法一：环境变量配置（推荐）**
```bash
# 设置DeepSeek API密钥（推荐，性价比高）
export DEEPSEEK_API_KEY="sk-your-deepseek-api-key"

# 或者设置OpenAI API密钥
export OPENAI_API_KEY="sk-your-openai-api-key"

# 或者设置Claude API密钥
export ANTHROPIC_API_KEY="sk-ant-your-claude-api-key"
```

**方法二：配置文件配置**
```bash
# 1. 复制配置文件
cp config/config.example.yaml config/config.yaml

# 2. 编辑config/config.yaml，在对应位置填入API密钥
# 找到 summarizer.deepseek.api_key 并填入你的密钥
```

#### 3. 运行系统
```bash
# 只运行论文爬虫（不需要API密钥）
python run.py --mode crawler

# 运行完整流水线（需要API密钥）
python run.py --mode full
```

## ⚙️ 配置说明

### API密钥配置
在GitHub仓库的Settings > Secrets中配置以下环境变量：
- `OPENAI_API_KEY`: OpenAI API密钥
- `DEEPSEEK_API_KEY`: DeepSeek API密钥
- `ANTHROPIC_API_KEY`: Claude API密钥
- `EMAIL_PASSWORD`: 邮件服务密码
- `SMTP_SERVER`: SMTP服务器地址

### 详细配置
编辑 `config/config.yaml` 文件进行详细配置：

#### 爬取配置
```yaml
crawler:
  sources:
    arxiv:
      enabled: true
      max_results: 50
      categories: ["cs.AI", "cs.LG", "cs.CV"]
      # keywords: []  # 已删除关键词限制，获取所有相关分类的论文
  strategy:
    date_range_days: 7
    max_papers_per_run: 100
```

#### AI摘要配置
```yaml
summarizer:
  default_provider: "deepseek"  # 可选: openai, deepseek, anthropic
  deepseek:
    model: "deepseek-reasoner"
    max_tokens: 1000
    base_url: "https://api.deepseek.com/v1"
  openai:
    model: "gpt-3.5-turbo"
    max_tokens: 500
  summary:
    language: "zh"  # zh: 中文, en: 英文
```

#### 邮件配置
```yaml
mailer:
  smtp:
    server: "smtp.gmail.com"
    username: "<EMAIL>"
  recipients:
    - email: "<EMAIL>"
      categories: ["cs.AI"]
```

## 🔧 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确配置
   - 确认API配额是否充足
   - 查看网络连接状态

2. **爬取失败**
   - 检查目标网站是否可访问
   - 确认爬取规则是否正确
   - 查看是否被反爬虫机制阻止

3. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认邮箱密码和权限
   - 查看防火墙设置

### 日志查看
```bash
# 查看最新日志
tail -f logs/app.log

# 查看特定模块日志
grep "crawler" logs/app.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 更新 CHANGELOG.md
5. 推送到分支 (`git push origin feature/AmazingFeature`)
6. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件至 [<EMAIL>]

---

**注意**: 本项目仍在开发中，功能可能不完整。欢迎贡献代码和提出建议！
