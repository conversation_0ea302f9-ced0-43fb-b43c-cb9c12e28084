<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction - 最新论文摘要</title>
    <meta name="description" content="$$\mathcal{L} = \sum_{t} \| \mathbf{I}_{\text{render}}(t) - \mathbf{I}_{\text{gt}}(t) \|_2^2 + \lambda \cdot \mathcal{R}_{\text{motion}}$$
其中 $\mathca...">
    <meta name="author" content="论文自动化系统">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- MathJax for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>

    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --accent-color: #10b981;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --font-family: Inter, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            line-height: 1.7;
            color: var(--text-primary);
            background-color: var(--background-color);
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background-color: var(--surface-color) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }

        .paper-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background-color: var(--surface-color);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .paper-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .paper-title {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            line-height: 1.4;
            display: block;
            margin-bottom: 0.5rem;
        }

        .paper-title:hover {
            color: var(--primary-dark);
            text-decoration: none;
        }

        .paper-authors {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        .paper-summary {
            font-size: 0.95rem;
            line-height: 1.6;
            color: var(--text-primary);
        }
        
        .category-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            text-decoration: none;
            display: inline-block;
            margin: 0.125rem;
            transition: all 0.2s ease;
        }

        .category-badge:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .key-points {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: var(--shadow-sm);
        }

        .key-points h6 {
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .key-points ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }

        .key-points li {
            margin-bottom: 0.5rem;
        }

        .footer {
            background: linear-gradient(135deg, var(--surface-color), var(--background-color));
            color: var(--text-secondary);
            padding: 3rem 0 2rem;
            margin-top: 4rem;
            border-top: 1px solid var(--border-color);
        }

        .search-box {
            max-width: 400px;
        }

        .search-box .form-control {
            border-radius: 25px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
        }

        .search-box .btn {
            border-radius: 25px;
            padding: 0.5rem 1rem;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: none;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--accent-color));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Markdown 内容样式 */
        .markdown-content {
            line-height: 1.8;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .markdown-content h1 { font-size: 2rem; }
        .markdown-content h2 { font-size: 1.5rem; }
        .markdown-content h3 { font-size: 1.25rem; }

        .markdown-content p {
            margin-bottom: 1rem;
        }

        .markdown-content ul, .markdown-content ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }

        .markdown-content li {
            margin-bottom: 0.5rem;
        }

        .markdown-content strong {
            color: var(--primary-color);
            font-weight: 600;
        }

        .markdown-content code {
            background-color: var(--background-color);
            color: var(--primary-color);
            padding: 0.125rem 0.25rem;
            border-radius: 4px;
            font-family: var(--font-mono);
            font-size: 0.9em;
        }

        .markdown-content pre {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-content pre code {
            background: none;
            padding: 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--primary-color);
            background-color: var(--background-color);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }

        /* 代码高亮样式 */
        .highlight {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .highlight pre {
            margin: 0;
            background: none;
            border: none;
            padding: 0;
        }

        .highlight .hll { background-color: #ffffcc }
        .highlight .c { color: #8f5902; font-style: italic } /* Comment */
        .highlight .err { color: #a40000; border: 1px solid #ef2929 } /* Error */
        .highlight .g { color: #000000 } /* Generic */
        .highlight .k { color: #204a87; font-weight: bold } /* Keyword */
        .highlight .l { color: #000000 } /* Literal */
        .highlight .n { color: #000000 } /* Name */
        .highlight .o { color: #ce5c00; font-weight: bold } /* Operator */
        .highlight .x { color: #000000 } /* Other */
        .highlight .p { color: #000000; font-weight: bold } /* Punctuation */
        .highlight .ch { color: #8f5902; font-style: italic } /* Comment.Hashbang */
        .highlight .cm { color: #8f5902; font-style: italic } /* Comment.Multiline */
        .highlight .cp { color: #8f5902; font-style: italic } /* Comment.Preproc */
        .highlight .cpf { color: #8f5902; font-style: italic } /* Comment.PreprocFile */
        .highlight .c1 { color: #8f5902; font-style: italic } /* Comment.Single */
        .highlight .cs { color: #8f5902; font-style: italic } /* Comment.Special */
        .highlight .gd { color: #a40000 } /* Generic.Deleted */
        .highlight .ge { color: #000000; font-style: italic } /* Generic.Emph */
        .highlight .gr { color: #ef2929 } /* Generic.Error */
        .highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
        .highlight .gi { color: #00A000 } /* Generic.Inserted */
        .highlight .go { color: #000000; font-style: italic } /* Generic.Output */
        .highlight .gp { color: #8f5902 } /* Generic.Prompt */
        .highlight .gs { color: #000000; font-weight: bold } /* Generic.Strong */
        .highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
        .highlight .gt { color: #a40000; font-weight: bold } /* Generic.Traceback */
        .highlight .kc { color: #204a87; font-weight: bold } /* Keyword.Constant */
        .highlight .kd { color: #204a87; font-weight: bold } /* Keyword.Declaration */
        .highlight .kn { color: #204a87; font-weight: bold } /* Keyword.Namespace */
        .highlight .kp { color: #204a87; font-weight: bold } /* Keyword.Pseudo */
        .highlight .kr { color: #204a87; font-weight: bold } /* Keyword.Reserved */
        .highlight .kt { color: #204a87; font-weight: bold } /* Keyword.Type */
        .highlight .ld { color: #000000 } /* Literal.Date */
        .highlight .m { color: #0000cf; font-weight: bold } /* Literal.Number */
        .highlight .s { color: #4e9a06 } /* Literal.String */
        .highlight .na { color: #c4a000 } /* Name.Attribute */
        .highlight .nb { color: #204a87 } /* Name.Builtin */
        .highlight .nc { color: #000000 } /* Name.Class */
        .highlight .no { color: #000000 } /* Name.Constant */
        .highlight .nd { color: #5c35cc; font-weight: bold } /* Name.Decorator */
        .highlight .ni { color: #ce5c00 } /* Name.Entity */
        .highlight .ne { color: #cc0000; font-weight: bold } /* Name.Exception */
        .highlight .nf { color: #000000 } /* Name.Function */
        .highlight .nl { color: #f57900 } /* Name.Label */
        .highlight .nn { color: #000000 } /* Name.Namespace */
        .highlight .nx { color: #000000 } /* Name.Other */
        .highlight .py { color: #000000 } /* Name.Property */
        .highlight .nt { color: #204a87; font-weight: bold } /* Name.Tag */
        .highlight .nv { color: #000000 } /* Name.Variable */
        .highlight .ow { color: #204a87; font-weight: bold } /* Operator.Word */
        .highlight .w { color: #f8f8f8; text-decoration: underline } /* Text.Whitespace */
        .highlight .mb { color: #0000cf; font-weight: bold } /* Literal.Number.Bin */
        .highlight .mf { color: #0000cf; font-weight: bold } /* Literal.Number.Float */
        .highlight .mh { color: #0000cf; font-weight: bold } /* Literal.Number.Hex */
        .highlight .mi { color: #0000cf; font-weight: bold } /* Literal.Number.Integer */
        .highlight .mo { color: #0000cf; font-weight: bold } /* Literal.Number.Oct */
        .highlight .sa { color: #4e9a06 } /* Literal.String.Affix */
        .highlight .sb { color: #4e9a06 } /* Literal.String.Backtick */
        .highlight .sc { color: #4e9a06 } /* Literal.String.Char */
        .highlight .dl { color: #4e9a06 } /* Literal.String.Delimiter */
        .highlight .sd { color: #8f5902; font-style: italic } /* Literal.String.Doc */
        .highlight .s2 { color: #4e9a06 } /* Literal.String.Double */
        .highlight .se { color: #4e9a06 } /* Literal.String.Escape */
        .highlight .sh { color: #4e9a06 } /* Literal.String.Heredoc */
        .highlight .si { color: #4e9a06 } /* Literal.String.Interpol */
        .highlight .sx { color: #4e9a06 } /* Literal.String.Other */
        .highlight .sr { color: #4e9a06 } /* Literal.String.Regex */
        .highlight .s1 { color: #4e9a06 } /* Literal.String.Single */
        .highlight .ss { color: #4e9a06 } /* Literal.String.Symbol */
        .highlight .bp { color: #3465a4 } /* Name.Builtin.Pseudo */
        .highlight .fm { color: #000000 } /* Name.Function.Magic */
        .highlight .vc { color: #000000 } /* Name.Variable.Class */
        .highlight .vg { color: #000000 } /* Name.Variable.Global */
        .highlight .vi { color: #000000 } /* Name.Variable.Instance */
        .highlight .vm { color: #000000 } /* Name.Variable.Magic */
        .highlight .il { color: #0000cf; font-weight: bold } /* Literal.Number.Integer.Long */
        
        
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
                color: #e5e5e5;
            }
            
            .paper-card {
                background-color: #2d2d2d;
                color: #e5e5e5;
            }
            
            .navbar {
                background-color: #2d2d2d !important;
            }
            
            .footer {
                background-color: #2d2d2d;
            }
        }
        
    </style>
    
    
<!-- Open Graph Meta Tags -->
<meta property="og:title" content="FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction">
<meta property="og:description" content="$$\mathcal{L} = \sum_{t} \| \mathbf{I}_{\text{render}}(t) - \mathbf{I}_{\text{gt}}(t) \|_2^2 + \lambda \cdot \mathcal{R}_{\text{motion}}$$
其中 $\mathca...">
<meta property="og:type" content="article">
<meta property="og:url" content="https://your-username.github.io/paper-automation/papers/2506.05348v1.html">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary">
<meta name="twitter:title" content="FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction">
<meta name="twitter:description" content="$$\mathcal{L} = \sum_{t} \| \mathbf{I}_{\text{render}}(t) - \mathbf{I}_{\text{gt}}(t) \|_2^2 + \lambda \cdot \mathcal{R}_{\text{motion}}$$
其中 $\mathca...">

</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>
                最新论文摘要
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tags me-1"></i>分类
                        </a>
                        <ul class="dropdown-menu">
                            
                            <li><a class="dropdown-item" href="/categories/cs.CV.html">cs.CV</a></li>
                            
                            <li><a class="dropdown-item" href="/categories/cs.LG.html">cs.LG</a></li>
                            
                            <li><a class="dropdown-item" href="/categories/cs.CL.html">cs.CL</a></li>
                            
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/rss.xml" target="_blank">
                            <i class="fas fa-rss me-1"></i>RSS
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <div class="d-flex search-box">
                    <input class="form-control me-2" type="search" placeholder="搜索论文..." id="searchInput">
                    <button class="btn btn-outline-primary" type="button" onclick="searchPapers()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-4">
        
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">首页</a></li>
        <li class="breadcrumb-item active">论文详情</li>
    </ol>
</nav>

<div class="row">
    <div class="col-lg-8">
        <!-- 论文详情卡片 -->
        <div class="card paper-card">
            <div class="card-body">
                <!-- 论文标题 -->
                <h1 class="card-title h3 mb-3">FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction</h1>
                
                <!-- 作者信息 -->
                <div class="mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-users me-2"></i>作者
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        
                        <span class="badge bg-light text-dark">Yifan Wang</span>
                        
                        <span class="badge bg-light text-dark">Peishan Yang</span>
                        
                        <span class="badge bg-light text-dark">Zhen Xu</span>
                        
                        <span class="badge bg-light text-dark">Jiaming Sun</span>
                        
                        <span class="badge bg-light text-dark">Zhanhua Zhang</span>
                        
                        <span class="badge bg-light text-dark">Yong Chen</span>
                        
                        <span class="badge bg-light text-dark">Hujun Bao</span>
                        
                        <span class="badge bg-light text-dark">Sida Peng</span>
                        
                        <span class="badge bg-light text-dark">Xiaowei Zhou</span>
                        
                    </div>
                </div>
                
                <!-- 元信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-calendar me-2"></i>发布时间
                        </h6>
                        <p>2025年06月05日</p>
                        
                        
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-edit me-2"></i>更新时间
                        </h6>
                        <p>2025年06月05日</p>
                        
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-source me-2"></i>来源
                        </h6>
                        <p>arxiv</p>
                        
                        
                    </div>
                </div>
                
                <!-- 分类标签 -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-tags me-2"></i>分类
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        
                        <a href="/categories/cs.CV.html" class="category-badge">
                            cs.CV
                        </a>
                        
                    </div>
                </div>
                
                <!-- 原始摘要 -->
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-file-alt me-2"></i>原始摘要
                    </h5>
                    <div class="border-start border-primary border-3 ps-3">
                        <p class="text-muted">This paper addresses the challenge of reconstructing dynamic 3D scenes with
complex motions. Some recent works define 3D Gaussian primitives in the
canonical space and use deformation fields to map canonical primitives to
observation spaces, achieving real-time dynamic view synthesis. However, these
methods often struggle to handle scenes with complex motions due to the
difficulty of optimizing deformation fields. To overcome this problem, we
propose FreeTimeGS, a novel 4D representation that allows Gaussian primitives
to appear at arbitrary time and locations. In contrast to canonical Gaussian
primitives, our representation possesses the strong flexibility, thus improving
the ability to model dynamic 3D scenes. In addition, we endow each Gaussian
primitive with an motion function, allowing it to move to neighboring regions
over time, which reduces the temporal redundancy. Experiments results on
several datasets show that the rendering quality of our method outperforms
recent methods by a large margin.</p>
                    </div>
                </div>
                
                <!-- AI生成摘要 -->
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-robot me-2"></i>AI生成摘要
                    </h5>
                    <div class="alert alert-info">
                        <div class="markdown-content"><p>$<strong>INLINE_MATH_0____INLINE_MATH_1</strong>\mathcal{R}_{\text{motion}}<strong>INLINE_MATH_2</strong>\</p></div>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            生成时间: 2025-06-08T21:20:19 |
                            模型: deepseek
                        </small>
                    </div>
                </div>
                
                <!-- 关键要点 -->
                
                
                <!-- 研究方法 -->
                

                <!-- 学术意义 -->
                

                <!-- 局限性 -->
                
                
                <!-- 操作按钮 -->
                <div class="d-flex gap-2 mb-3">
                    
                    <a href="http://arxiv.org/pdf/2506.05348v1" target="_blank" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>查看PDF
                    </a>
                    
                    
                    <button class="btn btn-outline-primary" onclick="sharePaper()">
                        <i class="fas fa-share me-2"></i>分享
                    </button>
                    
                    <button class="btn btn-outline-secondary" onclick="copyLink()">
                        <i class="fas fa-link me-2"></i>复制链接
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 快速信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>快速信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h5 mb-0">9</div>
                            <small class="text-muted">作者数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0">1</div>
                        <small class="text-muted">分类数</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相关分类 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tags me-2"></i>相关分类
                </h6>
            </div>
            <div class="card-body">
                
                <a href="/categories/cs.CV.html" class="d-block mb-2 text-decoration-none">
                    <i class="fas fa-folder me-2"></i>cs.CV
                </a>
                
            </div>
        </div>
        
        <!-- 论文统计 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>论文统计
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">摘要长度</small>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar" style="width: 51.0%"></div>
                    </div>
                    <small class="text-muted">1017 字符</small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">AI摘要长度</small>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: 19.0%"></div>
                    </div>
                    <small class="text-muted">188 字符</small>
                </div>
                
                
            </div>
        </div>
    </div>
</div>

    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>最新论文摘要</h6>
                    <p class="mb-0">AI自动生成的最新学术论文摘要</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-robot me-1"></i>
                        由AI自动生成 | 
                        <i class="fas fa-clock me-1"></i>
                        更新时间: 
                    </p>
                    <p class="mb-0">
                        <a href="https://github.com" target="_blank" class="text-decoration-none">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 搜索功能
        function searchPapers() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const papers = document.querySelectorAll('.paper-card');
            
            papers.forEach(paper => {
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const summary = paper.querySelector('.paper-summary').textContent.toLowerCase();
                const authors = paper.querySelector('.paper-authors').textContent.toLowerCase();
                
                if (title.includes(query) || summary.includes(query) || authors.includes(query)) {
                    paper.style.display = 'block';
                } else {
                    paper.style.display = 'none';
                }
            });
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchPapers();
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
    
    
<script>
// 分享功能
function sharePaper() {
    if (navigator.share) {
        navigator.share({
            title: 'FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction',
            text: '$$\mathcal{L} = \sum_{t} \| \mathbf{I}_{\text{render}}(t) - \mathbf{I}_{\text{gt}}(t) \|_2^2 + \lamb...',
            url: window.location.href
        });
    } else {
        // 降级到复制链接
        copyLink();
    }
}

// 复制链接
function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>链接已复制到剪贴板
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(() => {
        alert('复制失败，请手动复制链接');
    });
}

// 添加阅读进度条
const progressBar = document.createElement('div');
progressBar.id = 'reading-progress';
progressBar.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--primary-color);
    z-index: 9999;
    transition: width 0.3s ease;
`;
document.body.appendChild(progressBar);
</script>

</body>
</html>