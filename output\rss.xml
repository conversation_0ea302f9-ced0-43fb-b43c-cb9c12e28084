<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
        <title>最新论文摘要</title>
        <description>AI自动生成的最新学术论文摘要</description>
        <link>https://your-username.github.io/paper-automation</link>
        <atom:link href="https://your-username.github.io/paper-automation/rss.xml" rel="self" type="application/rss+xml" />
        <language>zh-CN</language>
        <lastBuildDate>2025-06-08T21:24:45.666745</lastBuildDate>
        <generator>论文自动化处理系统</generator>
        
        
        <item>
            <title><![CDATA[VideoMathQA: Benchmarking Mathematical Reasoning via Multimodal
  Understanding in Videos]]></title>
            <description><![CDATA[
                <p><strong>作者:</strong> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Shaker, <PERSON><PERSON>, <PERSON>, <PERSON>-<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON></p>
                <p><strong>AI摘要:</strong> - **领域影响**：填补了视频多模态推理基准的空白，促进社区标准化（类似ImageNet在CV中的作用）。潜在风险是如果模型在基准上表现差，可能阻碍实际部署，但公开资源鼓励创新。</p>
                
                <p><strong>关键要点:</strong></p>
                <ul>
                
                    <li>通过基准测试，论文揭示现有模型（如基于图像的MathVQA或文本模型）的不足：它们擅长感知但弱于推理，尤其在时间扩展任务上（如长视频中信息整合），错误集中于概念迁移和深度理解类。技术细节上，模型常忽略音频线索或误读手写文本，导致在10%的样本上完全失败。</li>
                
                </ul>
                
                <p><strong>分类:</strong> cs.CV</p>
                <p><strong>来源:</strong> arxiv</p>
                
                <p><a href="http://arxiv.org/pdf/2506.05349v1" target="_blank">查看PDF</a></p>
                
            ]]></description>
            <link>https://your-username.github.io/paper-automation/papers/2506.05349v1.html</link>
            <guid>https://your-username.github.io/paper-automation/papers/2506.05349v1.html</guid>
            <pubDate>Thu, 05 Jun 2025 17:59:58 +0000</pubDate>
            <category>cs.CV</category>
            <author>Hanoona Rasheed, Abdelrahman Shaker, Anqi Tang, Muhammad Maaz, Ming-Hsuan Yang, Salman Khan, Fahad Khan</author>
        </item>
        
        <item>
            <title><![CDATA[Contrastive Flow Matching]]></title>
            <description><![CDATA[
                <p><strong>作者:</strong> George Stoica, Vivek Ramanujan, Xiang Fan, Ali Farhadi, Ranjay Krishna, Judy Hoffman</p>
                <p><strong>AI摘要:</strong> $$\mathcal{L}_{contr} = -\mathbb{E}_{t, x_i, x_j} \left[ \text{sim}(v_\theta(t, x_i), v_\theta(t, x_j)) \right]$$
- **架构普适性**：CFM 是目标函数的扩展，**独立于模型架构**。实验验证了其在U-Net（ImageNet）和Transformer（CC3M）上的有效性，证明其广泛适用性。</p>
                
                <p><strong>关键要点:</strong></p>
                <ul>
                
                    <li>### 优势与适用性</li>
                
                    <li>**优势**：</li>
                
                    <li>1. **解耦条件流**：显式分离不同条件的生成路径，提升条件控制的精确性。</li>
                
                    <li>2. **加速训练**：对比损失引导更高效的梯度更新方向，大幅减少收敛时间。</li>
                
                    <li>3. **降低推理成本**：流空间分离使ODE求解更稳定，减少去噪步数。</li>
                
                    <li>**适用性**：适用于所有基于流匹配的条件生成任务（图像/视频/3D生成），无需修改模型主干。</li>
                
                </ul>
                
                <p><strong>分类:</strong> cs.CV</p>
                <p><strong>来源:</strong> arxiv</p>
                
                <p><a href="http://arxiv.org/pdf/2506.05350v1" target="_blank">查看PDF</a></p>
                
            ]]></description>
            <link>https://your-username.github.io/paper-automation/papers/2506.05350v1.html</link>
            <guid>https://your-username.github.io/paper-automation/papers/2506.05350v1.html</guid>
            <pubDate>Thu, 05 Jun 2025 17:59:58 +0000</pubDate>
            <category>cs.CV</category>
            <author>George Stoica, Vivek Ramanujan, Xiang Fan, Ali Farhadi, Ranjay Krishna, Judy Hoffman</author>
        </item>
        
        <item>
            <title><![CDATA[FreeTimeGS: Free Gaussians at Anytime and Anywhere for Dynamic Scene
  Reconstruction]]></title>
            <description><![CDATA[
                <p><strong>作者:</strong> Yifan Wang, Peishan Yang, Zhen Xu, Jiaming Sun, Zhanhua Zhang, Yong Chen, Hujun Bao, Sida Peng, Xiaowei Zhou</p>
                <p><strong>AI摘要:</strong> $$\mathcal{L} = \sum_{t} \| \mathbf{I}_{\text{render}}(t) - \mathbf{I}_{\text{gt}}(t) \|_2^2 + \lambda \cdot \mathcal{R}_{\text{motion}}$$
其中 $\mathcal{R}_{\text{motion}}$ 是运动正则化项（如平滑约束 $\</p>
                
                <p><strong>分类:</strong> cs.CV</p>
                <p><strong>来源:</strong> arxiv</p>
                
                <p><a href="http://arxiv.org/pdf/2506.05348v1" target="_blank">查看PDF</a></p>
                
            ]]></description>
            <link>https://your-username.github.io/paper-automation/papers/2506.05348v1.html</link>
            <guid>https://your-username.github.io/paper-automation/papers/2506.05348v1.html</guid>
            <pubDate>Thu, 05 Jun 2025 17:59:57 +0000</pubDate>
            <category>cs.CV</category>
            <author>Yifan Wang, Peishan Yang, Zhen Xu, Jiaming Sun, Zhanhua Zhang, Yong Chen, Hujun Bao, Sida Peng, Xiaowei Zhou</author>
        </item>
        
        <item>
            <title><![CDATA[SparseMM: Head Sparsity Emerges from Visual Concept Responses in MLLMs]]></title>
            <description><![CDATA[
                <p><strong>作者:</strong> Jiahui Wang, Zuyan Liu, Yongming Rao, Jiwen Lu</p>
                <p><strong>AI摘要:</strong> 2.  为理解Transformer在跨模态任务中的**参数效率**和**功能模块化**提供了新证据。
3.  验证了**预训练LLMs的可迁移性**高度依赖于特定子结构（视觉头），深化了对模型扩展本质的认识。
*   **实践价值:**
1.  提供了一种**高效、无损的MLLM推理加速方案** (SparseMM)，直接推动多模态应用（如具身智能、实时交互系统）的落地。
2.  提出的**无训练视觉头识别框架**为模型分析、结构搜索和高效微调提供了通用工具。
3.  **KV-Cache非对称优化**思想可推广至其他存在计算冗余的场景（如纯文本长序列推理）。
4.  开源项目促进社区在高效多模态计算领域的研究和应用。</p>
                
                <p><strong>关键要点:</strong></p>
                <ul>
                
                    <li>*   **即插即用:** SparseMM 作为推理期优化层，兼容现有MLLMs。</li>
                
                </ul>
                
                <p><strong>分类:</strong> cs.CV</p>
                <p><strong>来源:</strong> arxiv</p>
                
                <p><a href="http://arxiv.org/pdf/2506.05344v1" target="_blank">查看PDF</a></p>
                
            ]]></description>
            <link>https://your-username.github.io/paper-automation/papers/2506.05344v1.html</link>
            <guid>https://your-username.github.io/paper-automation/papers/2506.05344v1.html</guid>
            <pubDate>Thu, 05 Jun 2025 17:59:55 +0000</pubDate>
            <category>cs.CV</category>
            <author>Jiahui Wang, Zuyan Liu, Yongming Rao, Jiwen Lu</author>
        </item>
        
        <item>
            <title><![CDATA[Inference-Time Hyper-Scaling with KV Cache Compression]]></title>
            <description><![CDATA[
                <p><strong>作者:</strong> Adrian Łańcucki, Konrad Staniszewski, Piotr Nawrot, Edoardo M. Ponti</p>
                <p><strong>AI摘要:</strong> - **超扩展框架的本质是&#34;缓存-精度&#34;置换**：通过压缩KV缓存（如8×），在固定显存预算下允许生成4-8倍更多token，利用扩展推理路径提升复杂任务准确率，突破传统扩展的硬件天花板。</p>
                
                <p><strong>分类:</strong> cs.LG, cs.CL</p>
                <p><strong>来源:</strong> arxiv</p>
                
                <p><a href="http://arxiv.org/pdf/2506.05345v1" target="_blank">查看PDF</a></p>
                
            ]]></description>
            <link>https://your-username.github.io/paper-automation/papers/2506.05345v1.html</link>
            <guid>https://your-username.github.io/paper-automation/papers/2506.05345v1.html</guid>
            <pubDate>Thu, 05 Jun 2025 17:59:55 +0000</pubDate>
            <category>cs.LG, cs.CL</category>
            <author>Adrian Łańcucki, Konrad Staniszewski, Piotr Nawrot, Edoardo M. Ponti</author>
        </item>
        
    </channel>
</rss>