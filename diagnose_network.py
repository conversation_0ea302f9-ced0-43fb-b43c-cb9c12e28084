#!/usr/bin/env python3
"""
网络连接诊断脚本
用于诊断arXiv API连接问题
"""

import requests
import socket
import sys
from datetime import datetime, timedelta
from urllib.parse import urlencode

def test_basic_connectivity():
    """测试基本网络连接"""
    print("🔍 测试基本网络连接...")
    
    # 测试DNS解析
    try:
        ip = socket.gethostbyname('export.arxiv.org')
        print(f"✅ DNS解析成功: export.arxiv.org -> {ip}")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")
        return False
    
    # 测试TCP连接
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((ip, 80))
        sock.close()
        if result == 0:
            print("✅ TCP连接成功")
        else:
            print(f"❌ TCP连接失败: {result}")
            return False
    except Exception as e:
        print(f"❌ TCP连接测试失败: {e}")
        return False
    
    return True

def test_http_request():
    """测试HTTP请求"""
    print("\n🌐 测试HTTP请求...")
    
    # 简单的HTTP请求
    try:
        response = requests.get('http://export.arxiv.org/', timeout=10)
        print(f"✅ HTTP请求成功: 状态码 {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        print("❌ HTTP请求超时")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ HTTP连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ HTTP请求失败: {e}")
        return False

def test_arxiv_api():
    """测试arXiv API"""
    print("\n📚 测试arXiv API...")
    
    # 最简单的API请求
    try:
        url = "http://export.arxiv.org/api/query?search_query=cat:cs.AI&start=0&max_results=1"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"响应长度: {len(content)} 字符")
            
            if '<entry>' in content:
                print("✅ arXiv API工作正常，找到论文条目")
                return True
            else:
                print("⚠️ arXiv API响应正常但没有找到论文条目")
                print("响应内容前500字符:")
                print(content[:500])
                return False
        else:
            print(f"❌ arXiv API返回错误状态码: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ arXiv API请求超时")
        return False
    except Exception as e:
        print(f"❌ arXiv API请求失败: {e}")
        return False

def test_date_range_query():
    """测试日期范围查询"""
    print("\n📅 测试日期范围查询...")
    
    try:
        # 测试最近30天的查询
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        date_query = f'submittedDate:[{start_date.strftime("%Y%m%d")}0000 TO {end_date.strftime("%Y%m%d")}2359]'
        
        params = {
            'search_query': f'cat:cs.AI AND {date_query}',
            'start': 0,
            'max_results': 5,
            'sortBy': 'submittedDate',
            'sortOrder': 'descending'
        }
        
        url = f'http://export.arxiv.org/api/query?{urlencode(params)}'
        print(f"日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            entry_count = content.count('<entry>')
            print(f"找到 {entry_count} 篇论文")
            
            if entry_count > 0:
                print("✅ 日期范围查询成功")
                return True
            else:
                print("⚠️ 日期范围内没有找到论文")
                return False
        else:
            print(f"❌ 日期范围查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 日期范围查询失败: {e}")
        return False

def check_proxy_settings():
    """检查代理设置"""
    print("\n🔧 检查代理设置...")
    
    import os
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"发现代理设置: {var}={value}")
            proxy_found = True
    
    if not proxy_found:
        print("✅ 没有发现代理设置")
    else:
        print("⚠️ 发现代理设置，可能影响连接")
    
    return not proxy_found

def main():
    """主函数"""
    print("🚀 arXiv连接诊断工具")
    print("=" * 50)
    
    tests = [
        ("基本网络连接", test_basic_connectivity),
        ("HTTP请求", test_http_request),
        ("arXiv API", test_arxiv_api),
        ("日期范围查询", test_date_range_query),
        ("代理设置检查", check_proxy_settings),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 诊断结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！arXiv连接正常")
    else:
        print("⚠️ 部分测试失败，请检查网络连接和配置")
        print("\n🔧 建议的解决方案:")
        print("1. 检查网络连接是否正常")
        print("2. 检查防火墙设置")
        print("3. 检查代理配置")
        print("4. 尝试使用VPN或更换网络")
        print("5. 稍后重试（可能是临时网络问题）")

if __name__ == "__main__":
    main()
