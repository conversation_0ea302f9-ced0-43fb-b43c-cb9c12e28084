<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cs.CL - 最新论文摘要</title>
    <meta name="description" content="AI自动生成的最新学术论文摘要">
    <meta name="author" content="论文自动化系统">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- MathJax for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>

    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --accent-color: #10b981;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --font-family: Inter, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            line-height: 1.7;
            color: var(--text-primary);
            background-color: var(--background-color);
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background-color: var(--surface-color) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }

        .paper-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background-color: var(--surface-color);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .paper-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .paper-title {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            line-height: 1.4;
            display: block;
            margin-bottom: 0.5rem;
        }

        .paper-title:hover {
            color: var(--primary-dark);
            text-decoration: none;
        }

        .paper-authors {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        .paper-summary {
            font-size: 0.95rem;
            line-height: 1.6;
            color: var(--text-primary);
        }
        
        .category-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            text-decoration: none;
            display: inline-block;
            margin: 0.125rem;
            transition: all 0.2s ease;
        }

        .category-badge:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .key-points {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: var(--shadow-sm);
        }

        .key-points h6 {
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .key-points ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }

        .key-points li {
            margin-bottom: 0.5rem;
        }

        .footer {
            background: linear-gradient(135deg, var(--surface-color), var(--background-color));
            color: var(--text-secondary);
            padding: 3rem 0 2rem;
            margin-top: 4rem;
            border-top: 1px solid var(--border-color);
        }

        .search-box {
            max-width: 400px;
        }

        .search-box .form-control {
            border-radius: 25px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
        }

        .search-box .btn {
            border-radius: 25px;
            padding: 0.5rem 1rem;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: none;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--accent-color));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Markdown 内容样式 */
        .markdown-content {
            line-height: 1.8;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .markdown-content h1 { font-size: 2rem; }
        .markdown-content h2 { font-size: 1.5rem; }
        .markdown-content h3 { font-size: 1.25rem; }

        .markdown-content p {
            margin-bottom: 1rem;
        }

        .markdown-content ul, .markdown-content ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }

        .markdown-content li {
            margin-bottom: 0.5rem;
        }

        .markdown-content strong {
            color: var(--primary-color);
            font-weight: 600;
        }

        .markdown-content code {
            background-color: var(--background-color);
            color: var(--primary-color);
            padding: 0.125rem 0.25rem;
            border-radius: 4px;
            font-family: var(--font-mono);
            font-size: 0.9em;
        }

        .markdown-content pre {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-content pre code {
            background: none;
            padding: 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--primary-color);
            background-color: var(--background-color);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }

        /* 代码高亮样式 */
        .highlight {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .highlight pre {
            margin: 0;
            background: none;
            border: none;
            padding: 0;
        }

        .highlight .hll { background-color: #ffffcc }
        .highlight .c { color: #8f5902; font-style: italic } /* Comment */
        .highlight .err { color: #a40000; border: 1px solid #ef2929 } /* Error */
        .highlight .g { color: #000000 } /* Generic */
        .highlight .k { color: #204a87; font-weight: bold } /* Keyword */
        .highlight .l { color: #000000 } /* Literal */
        .highlight .n { color: #000000 } /* Name */
        .highlight .o { color: #ce5c00; font-weight: bold } /* Operator */
        .highlight .x { color: #000000 } /* Other */
        .highlight .p { color: #000000; font-weight: bold } /* Punctuation */
        .highlight .ch { color: #8f5902; font-style: italic } /* Comment.Hashbang */
        .highlight .cm { color: #8f5902; font-style: italic } /* Comment.Multiline */
        .highlight .cp { color: #8f5902; font-style: italic } /* Comment.Preproc */
        .highlight .cpf { color: #8f5902; font-style: italic } /* Comment.PreprocFile */
        .highlight .c1 { color: #8f5902; font-style: italic } /* Comment.Single */
        .highlight .cs { color: #8f5902; font-style: italic } /* Comment.Special */
        .highlight .gd { color: #a40000 } /* Generic.Deleted */
        .highlight .ge { color: #000000; font-style: italic } /* Generic.Emph */
        .highlight .gr { color: #ef2929 } /* Generic.Error */
        .highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
        .highlight .gi { color: #00A000 } /* Generic.Inserted */
        .highlight .go { color: #000000; font-style: italic } /* Generic.Output */
        .highlight .gp { color: #8f5902 } /* Generic.Prompt */
        .highlight .gs { color: #000000; font-weight: bold } /* Generic.Strong */
        .highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
        .highlight .gt { color: #a40000; font-weight: bold } /* Generic.Traceback */
        .highlight .kc { color: #204a87; font-weight: bold } /* Keyword.Constant */
        .highlight .kd { color: #204a87; font-weight: bold } /* Keyword.Declaration */
        .highlight .kn { color: #204a87; font-weight: bold } /* Keyword.Namespace */
        .highlight .kp { color: #204a87; font-weight: bold } /* Keyword.Pseudo */
        .highlight .kr { color: #204a87; font-weight: bold } /* Keyword.Reserved */
        .highlight .kt { color: #204a87; font-weight: bold } /* Keyword.Type */
        .highlight .ld { color: #000000 } /* Literal.Date */
        .highlight .m { color: #0000cf; font-weight: bold } /* Literal.Number */
        .highlight .s { color: #4e9a06 } /* Literal.String */
        .highlight .na { color: #c4a000 } /* Name.Attribute */
        .highlight .nb { color: #204a87 } /* Name.Builtin */
        .highlight .nc { color: #000000 } /* Name.Class */
        .highlight .no { color: #000000 } /* Name.Constant */
        .highlight .nd { color: #5c35cc; font-weight: bold } /* Name.Decorator */
        .highlight .ni { color: #ce5c00 } /* Name.Entity */
        .highlight .ne { color: #cc0000; font-weight: bold } /* Name.Exception */
        .highlight .nf { color: #000000 } /* Name.Function */
        .highlight .nl { color: #f57900 } /* Name.Label */
        .highlight .nn { color: #000000 } /* Name.Namespace */
        .highlight .nx { color: #000000 } /* Name.Other */
        .highlight .py { color: #000000 } /* Name.Property */
        .highlight .nt { color: #204a87; font-weight: bold } /* Name.Tag */
        .highlight .nv { color: #000000 } /* Name.Variable */
        .highlight .ow { color: #204a87; font-weight: bold } /* Operator.Word */
        .highlight .w { color: #f8f8f8; text-decoration: underline } /* Text.Whitespace */
        .highlight .mb { color: #0000cf; font-weight: bold } /* Literal.Number.Bin */
        .highlight .mf { color: #0000cf; font-weight: bold } /* Literal.Number.Float */
        .highlight .mh { color: #0000cf; font-weight: bold } /* Literal.Number.Hex */
        .highlight .mi { color: #0000cf; font-weight: bold } /* Literal.Number.Integer */
        .highlight .mo { color: #0000cf; font-weight: bold } /* Literal.Number.Oct */
        .highlight .sa { color: #4e9a06 } /* Literal.String.Affix */
        .highlight .sb { color: #4e9a06 } /* Literal.String.Backtick */
        .highlight .sc { color: #4e9a06 } /* Literal.String.Char */
        .highlight .dl { color: #4e9a06 } /* Literal.String.Delimiter */
        .highlight .sd { color: #8f5902; font-style: italic } /* Literal.String.Doc */
        .highlight .s2 { color: #4e9a06 } /* Literal.String.Double */
        .highlight .se { color: #4e9a06 } /* Literal.String.Escape */
        .highlight .sh { color: #4e9a06 } /* Literal.String.Heredoc */
        .highlight .si { color: #4e9a06 } /* Literal.String.Interpol */
        .highlight .sx { color: #4e9a06 } /* Literal.String.Other */
        .highlight .sr { color: #4e9a06 } /* Literal.String.Regex */
        .highlight .s1 { color: #4e9a06 } /* Literal.String.Single */
        .highlight .ss { color: #4e9a06 } /* Literal.String.Symbol */
        .highlight .bp { color: #3465a4 } /* Name.Builtin.Pseudo */
        .highlight .fm { color: #000000 } /* Name.Function.Magic */
        .highlight .vc { color: #000000 } /* Name.Variable.Class */
        .highlight .vg { color: #000000 } /* Name.Variable.Global */
        .highlight .vi { color: #000000 } /* Name.Variable.Instance */
        .highlight .vm { color: #000000 } /* Name.Variable.Magic */
        .highlight .il { color: #0000cf; font-weight: bold } /* Literal.Number.Integer.Long */
        
        
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
                color: #e5e5e5;
            }
            
            .paper-card {
                background-color: #2d2d2d;
                color: #e5e5e5;
            }
            
            .navbar {
                background-color: #2d2d2d !important;
            }
            
            .footer {
                background-color: #2d2d2d;
            }
        }
        
    </style>
    
    
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>
                最新论文摘要
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tags me-1"></i>分类
                        </a>
                        <ul class="dropdown-menu">
                            
                            <li><a class="dropdown-item" href="/categories/cs.CV.html">cs.CV</a></li>
                            
                            <li><a class="dropdown-item" href="/categories/cs.LG.html">cs.LG</a></li>
                            
                            <li><a class="dropdown-item" href="/categories/cs.CL.html">cs.CL</a></li>
                            
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/rss.xml" target="_blank">
                            <i class="fas fa-rss me-1"></i>RSS
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <div class="d-flex search-box">
                    <input class="form-control me-2" type="search" placeholder="搜索论文..." id="searchInput">
                    <button class="btn btn-outline-primary" type="button" onclick="searchPapers()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-4">
        
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">首页</a></li>
        <li class="breadcrumb-item active">cs.CL</li>
    </ol>
</nav>

<!-- 分类标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-tag me-2"></i>cs.CL
        <span class="badge bg-primary ms-2">1 篇</span>
    </h1>
    <a href="/" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-2"></i>返回首页
    </a>
</div>

<!-- 论文列表 -->
<div class="row">
    
    <div class="col-12 mb-4">
        <div class="card paper-card">
            <div class="card-body">
                <h5 class="card-title">
                    <a href="/papers/2506.05345v1.html" class="paper-title">
                        Inference-Time Hyper-Scaling with KV Cache Compression
                    </a>
                </h5>
                
                <div class="paper-authors mb-2">
                    <i class="fas fa-users me-1"></i>
                    Adrian Łańcucki, Konrad Staniszewski, Piotr Nawrot, Edoardo M. Ponti
                    <span class="ms-3">
                        <i class="fas fa-calendar me-1"></i>
                        2025-06-05
                    </span>
                </div>
                
                <div class="paper-summary mb-3">
                    - **超扩展框架的本质是&#34;缓存-精度&#34;置换**：通过压缩KV缓存（如8×），在固定显存预算下允许生成4-8倍更多token，利用扩展推理路径提升复杂任务准确率，突破传统扩展的硬件天花板。
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        
                        <a href="http://arxiv.org/pdf/2506.05345v1" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-pdf me-1"></i>PDF
                        </a>
                        
                        <a href="/papers/2506.05345v1.html" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>详情
                        </a>
                    </div>
                    <small class="text-muted">arxiv</small>
                </div>
            </div>
        </div>
    </div>
    
</div>



    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>最新论文摘要</h6>
                    <p class="mb-0">AI自动生成的最新学术论文摘要</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-robot me-1"></i>
                        由AI自动生成 | 
                        <i class="fas fa-clock me-1"></i>
                        更新时间: 
                    </p>
                    <p class="mb-0">
                        <a href="https://github.com" target="_blank" class="text-decoration-none">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 搜索功能
        function searchPapers() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const papers = document.querySelectorAll('.paper-card');
            
            papers.forEach(paper => {
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const summary = paper.querySelector('.paper-summary').textContent.toLowerCase();
                const authors = paper.querySelector('.paper-authors').textContent.toLowerCase();
                
                if (title.includes(query) || summary.includes(query) || authors.includes(query)) {
                    paper.style.display = 'block';
                } else {
                    paper.style.display = 'none';
                }
            });
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchPapers();
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
    
    
</body>
</html>