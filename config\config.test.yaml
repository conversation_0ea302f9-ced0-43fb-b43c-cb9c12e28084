# 测试配置文件 - 用于快速测试系统功能

# 基础配置
app:
  name: "论文自动化处理系统"
  version: "0.1.0"
  debug: true
  log_level: "DEBUG"
  timezone: "Asia/Shanghai"

# 论文爬取配置
crawler:
  sources:
    arxiv:
      enabled: true
      base_url: "http://export.arxiv.org/api/query"
      max_results: 5  # 测试时只爬取5篇
      categories:
        - "cs.AI"
        - "cs.LG"
      keywords:
        - "machine learning"
        - "deep learning"
  
  strategy:
    interval_hours: 24
    max_papers_per_run: 10  # 测试时限制数量
    duplicate_check: true
    date_range_days: 3  # 只爬取最近3天
  
  filters:
    min_abstract_length: 50  # 降低要求便于测试
    exclude_keywords: []
    include_keywords: []

# AI 摘要生成配置
summarizer:
  default_provider: "openai"
  
  openai:
    api_key: ""  # 从环境变量读取
    model: "gpt-3.5-turbo"
    max_tokens: 300  # 测试时减少token使用
    temperature: 0.3
    timeout: 30
  
  anthropic:
    api_key: ""  # 从环境变量读取
    model: "claude-3-haiku-20240307"  # 使用更便宜的模型
    max_tokens: 300
    temperature: 0.3
    timeout: 30
  
  summary:
    max_length: 200
    min_length: 50
    language: "zh"
    style: "academic"
    include_keywords: true
    include_methodology: true

# 网页渲染配置
renderer:
  output:
    directory: "output"
    static_files: "static"
    template_dir: "templates"
  
  site:
    title: "测试论文摘要"
    description: "AI自动生成的测试论文摘要"
    author: "测试系统"
    url: "https://test.github.io/paper-automation"
  
  pages:
    papers_per_page: 10
    enable_search: true
    enable_categories: true
    enable_tags: true
    enable_rss: true
  
  theme:
    primary_color: "#2563eb"
    secondary_color: "#64748b"
    font_family: "Inter, sans-serif"
    dark_mode: true

# 邮件推送配置（测试时禁用）
mailer:
  smtp:
    server: "smtp.gmail.com"
    port: 587
    use_tls: true
    username: ""
    password: ""
  
  email:
    from_name: "测试系统"
    subject_template: "📚 测试论文摘要 - {date}"
    max_papers_per_email: 5
    include_full_abstract: false
    include_pdf_links: true
  
  recipients: []  # 测试时不发送邮件
  
  schedule:
    enabled: false  # 测试时禁用邮件

# GitHub Actions 配置
github:
  repository:
    owner: "test-user"
    name: "paper-automation"
    branch: "main"
  
  pages:
    enabled: false  # 测试时禁用
  
  workflow:
    schedule: "0 9 * * *"
    timeout_minutes: 30

# 数据存储配置
storage:
  database:
    type: "sqlite"
    path: "data/test_papers.db"
  
  cache:
    enabled: true
    ttl_hours: 24
    max_size_mb: 100

# 日志配置
logging:
  level: "DEBUG"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/test.log"
  rotation: "1 day"
  retention: "3 days"
