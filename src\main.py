#!/usr/bin/env python3
"""
论文自动化处理系统主入口
"""

import asyncio
import argparse
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config import ConfigManager
from src.utils.logger import setup_logger
from src.utils.database import PaperDatabase
from src.crawler.paper_crawler import PaperCrawler
from src.summarizer.ai_summarizer import AISummarizer
from src.renderer.web_renderer import WebRenderer
from src.mailer.email_sender import EmailSender


class PaperAutomationSystem:
    """论文自动化处理系统主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化系统"""
        self.config = ConfigManager(config_path)
        self.logger = setup_logger(self.config)

        # 初始化数据库
        db_path = self.config.get('storage.database.path', 'data/papers.db')
        self.database = PaperDatabase(db_path)

        # 初始化各个模块
        self.crawler = PaperCrawler(self.config)
        self.summarizer = AISummarizer(self.config)
        self.renderer = WebRenderer(self.config)
        self.mailer = EmailSender(self.config)
    
    async def run_full_pipeline(self):
        """运行完整的处理流水线"""
        try:
            self.logger.info("开始执行论文自动化处理流水线")
            
            # 1. 爬取论文
            self.logger.info("步骤 1: 爬取最新论文")
            papers = await self.crawler.crawl_papers()
            self.logger.info(f"成功爬取 {len(papers)} 篇论文")

            if not papers:
                self.logger.warning("没有爬取到新论文，流水线结束")
                return

            # 1.1 保存论文到数据库
            self.logger.info("步骤 1.1: 保存论文到数据库")
            saved_count = self.database.save_papers(papers)
            self.logger.info(f"成功保存 {saved_count} 篇论文到数据库")
            
            # 2. 生成摘要
            self.logger.info("步骤 2: 生成AI摘要")
            summarized_papers = await self.summarizer.generate_summaries(papers)
            self.logger.info(f"成功生成 {len(summarized_papers)} 篇论文摘要")

            # 2.1 保存摘要到数据库
            self.logger.info("步骤 2.1: 保存摘要到数据库")
            for summary in summarized_papers:
                self.database.save_summary(summary)
            
            # 3. 渲染网页
            self.logger.info("步骤 3: 渲染网页")
            await self.renderer.render_website(summarized_papers)
            self.logger.info("网页渲染完成")
            
            # 4. 发送邮件
            if self.config.get('mailer.schedule.enabled', False):
                self.logger.info("步骤 4: 发送邮件通知")
                await self.mailer.send_daily_digest(summarized_papers)
                self.logger.info("邮件发送完成")
            
            self.logger.info("论文自动化处理流水线执行完成")
            
        except Exception as e:
            self.logger.error(f"流水线执行失败: {str(e)}", exc_info=True)
            raise
    



async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="论文自动化处理系统")
    parser.add_argument(
        "--config", 
        type=str, 
        default="config/config.yaml",
        help="配置文件路径"
    )

    parser.add_argument(
        "--module",
        type=str,
        choices=["crawler", "summarizer", "renderer", "mailer"],
        help="只运行指定模块"
    )
    
    args = parser.parse_args()
    
    try:
        # 初始化系统
        system = PaperAutomationSystem(args.config)
        
        if args.module:
            # 单模块运行
            if args.module == "crawler":
                papers = await system.crawler.crawl_papers()
                print(f"爬取到 {len(papers)} 篇论文")
            elif args.module == "summarizer":
                # 需要先有论文数据
                print("请先运行爬虫模块获取论文数据")
            elif args.module == "renderer":
                await system.renderer.render_website([])
                print("网页渲染完成")
            elif args.module == "mailer":
                await system.mailer.send_daily_digest([])
                print("邮件发送完成")
        else:
            # 运行完整流水线
            await system.run_full_pipeline()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
