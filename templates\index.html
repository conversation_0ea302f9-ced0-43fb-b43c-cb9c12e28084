{% extends "base.html" %}

{% block title %}{{ site.title }} - 最新论文摘要{% endblock %}

{% block content %}
<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ stats.total_papers }}</div>
            <div>篇论文</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ stats.categories_count }}</div>
            <div>个分类</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ stats.sources|length }}</div>
            <div>个来源</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">
                {% if stats.latest_date %}
                {{ stats.latest_date.strftime('%m-%d') }}
                {% else %}
                --
                {% endif %}
            </div>
            <div>最新更新</div>
        </div>
    </div>
</div>

<!-- 快速导航 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tags me-2"></i>热门分类
                </h5>
                <div class="d-flex flex-wrap gap-2">
                    {% for category, papers in categories.items() %}
                    <a href="/categories/{{ category }}.html" class="category-badge">
                        {{ category }} ({{ papers|length }})
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 论文列表 -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>
                <i class="fas fa-newspaper me-2"></i>最新论文摘要
            </h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortPapers('date')">
                    <i class="fas fa-calendar me-1"></i>按日期
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortPapers('title')">
                    <i class="fas fa-sort-alpha-down me-1"></i>按标题
                </button>
            </div>
        </div>
        
        <div id="papers-container">
            {% for summary in summaries %}
            <div class="card paper-card mb-4" data-date="{{ summary.paper.published_date.isoformat() }}" data-title="{{ summary.paper.title }}">
                <div class="card-body">
                    <!-- 论文标题 -->
                    <h5 class="card-title">
                        <a href="/papers/{{ summary.paper.id }}.html" class="paper-title">
                            {{ summary.paper.title }}
                        </a>
                    </h5>
                    
                    <!-- 作者和元信息 -->
                    <div class="paper-authors mb-2">
                        <i class="fas fa-users me-1"></i>
                        {{ summary.paper.authors|join(', ') }}
                        <span class="ms-3">
                            <i class="fas fa-calendar me-1"></i>
                            {{ summary.paper.published_date.strftime('%Y-%m-%d') }}
                        </span>
                        <span class="ms-3">
                            <i class="fas fa-source me-1"></i>
                            {{ summary.paper.source }}
                        </span>
                    </div>
                    
                    <!-- 分类标签 -->
                    <div class="mb-3">
                        {% for category in summary.paper.categories %}
                        <a href="/categories/{{ category }}.html" class="category-badge me-1">
                            {{ category }}
                        </a>
                        {% endfor %}
                    </div>
                    
                    <!-- AI摘要 -->
                    <div class="paper-summary mb-3">
                        <strong>AI摘要:</strong><br>
                        <div class="markdown-content">{{ summary.summary[:300] | markdown | safe }}{% if summary.summary|length > 300 %}...{% endif %}</div>
                    </div>
                    
                    <!-- 关键要点 -->
                    {% if summary.key_points %}
                    <div class="key-points">
                        <h6>
                            <i class="fas fa-lightbulb me-1"></i>关键要点:
                        </h6>
                        <ul class="mb-0">
                            {% for point in summary.key_points %}
                            <li>{{ point }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            {% if summary.paper.pdf_url %}
                            <a href="{{ summary.paper.pdf_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-pdf me-1"></i>查看PDF
                            </a>
                            {% endif %}
                            <a href="/papers/{{ summary.paper.id }}.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>详细信息
                            </a>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-robot me-1"></i>
                            由 {{ summary.model_used }} 生成
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 分页 -->
        {% if total_pages > 1 %}
        <nav aria-label="论文分页">
            <ul class="pagination justify-content-center">
                {% if current_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page - 1 }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in range(1, total_pages + 1) %}
                <li class="page-item {% if page_num == current_page %}active{% endif %}">
                    <a class="page-link" href="?page={{ page_num }}">{{ page_num }}</a>
                </li>
                {% endfor %}
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page + 1 }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 空状态 -->
{% if not summaries %}
<div class="text-center py-5">
    <i class="fas fa-search fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">暂无论文摘要</h3>
    <p class="text-muted">系统正在努力为您收集最新的学术论文，请稍后再来查看。</p>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// 排序功能
function sortPapers(sortBy) {
    const container = document.getElementById('papers-container');
    const papers = Array.from(container.children);
    
    papers.sort((a, b) => {
        if (sortBy === 'date') {
            const dateA = new Date(a.dataset.date);
            const dateB = new Date(b.dataset.date);
            return dateB - dateA; // 降序
        } else if (sortBy === 'title') {
            const titleA = a.dataset.title.toLowerCase();
            const titleB = b.dataset.title.toLowerCase();
            return titleA.localeCompare(titleB); // 升序
        }
    });
    
    // 重新排列DOM元素
    papers.forEach(paper => container.appendChild(paper));
    
    // 更新按钮状态
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// 懒加载图片（如果有的话）
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// 添加阅读进度指示器
window.addEventListener('scroll', () => {
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrolled = (winScroll / height) * 100;
    
    // 如果有进度条元素的话
    const progressBar = document.getElementById('reading-progress');
    if (progressBar) {
        progressBar.style.width = scrolled + '%';
    }
});
</script>
{% endblock %}
