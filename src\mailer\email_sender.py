"""
邮件推送模块
"""

import smtplib
import ssl
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Any
from jinja2 import Environment, BaseLoader

from src.summarizer.ai_summarizer import PaperSummary
from src.utils.logger import get_module_logger

logger = get_module_logger("mailer")


class EmailSender:
    """邮件发送器"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.mailer_config = self.config.get_section('mailer')
        
        # SMTP配置
        self.smtp_config = self.mailer_config.get('smtp', {})
        self.server = self.smtp_config.get('server', 'smtp.gmail.com')
        self.port = self.smtp_config.get('port', 587)
        self.use_tls = self.smtp_config.get('use_tls', True)
        self.username = self.smtp_config.get('username', '')
        self.password = self.smtp_config.get('password', '')
        
        # 邮件配置
        self.email_config = self.mailer_config.get('email', {})
        self.from_name = self.email_config.get('from_name', '论文自动化系统')
        self.subject_template = self.email_config.get('subject_template', '📚 今日论文摘要 - {date}')
        self.max_papers_per_email = self.email_config.get('max_papers_per_email', 10)
        
        # 收件人配置
        self.recipients = self.mailer_config.get('recipients', [])
        
        # 初始化模板引擎
        self.jinja_env = Environment(loader=BaseLoader())
    
    async def send_daily_digest(self, summaries: List[PaperSummary]):
        """
        发送每日论文摘要
        
        Args:
            summaries: 论文摘要列表
        """
        if not summaries:
            logger.info("没有论文摘要，跳过邮件发送")
            return
        
        if not self.recipients:
            logger.warning("没有配置收件人，跳过邮件发送")
            return
        
        try:
            logger.info(f"开始发送每日摘要邮件，共 {len(summaries)} 篇论文")
            
            # 按收件人分组发送
            for recipient in self.recipients:
                await self._send_to_recipient(recipient, summaries)
            
            logger.info("每日摘要邮件发送完成")
            
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            raise
    
    async def _send_to_recipient(self, recipient: Dict[str, Any], summaries: List[PaperSummary]):
        """向单个收件人发送邮件"""
        try:
            email = recipient.get('email')
            name = recipient.get('name', email)
            categories = recipient.get('categories', [])
            
            if not email:
                logger.warning(f"收件人配置无效: {recipient}")
                return
            
            # 过滤论文（如果指定了分类）
            filtered_summaries = summaries
            if categories:
                filtered_summaries = [
                    s for s in summaries 
                    if any(cat in s.paper.categories for cat in categories)
                ]
            
            if not filtered_summaries:
                logger.info(f"收件人 {name} 没有匹配的论文，跳过发送")
                return
            
            # 限制论文数量
            if len(filtered_summaries) > self.max_papers_per_email:
                filtered_summaries = filtered_summaries[:self.max_papers_per_email]
            
            # 生成邮件内容
            subject = self._generate_subject()
            html_content = self._generate_html_content(filtered_summaries, name)
            text_content = self._generate_text_content(filtered_summaries, name)
            
            # 发送邮件
            await self._send_email(email, name, subject, html_content, text_content)
            
            logger.info(f"邮件已发送给 {name} ({email})，包含 {len(filtered_summaries)} 篇论文")
            
        except Exception as e:
            logger.error(f"向 {recipient.get('email', 'unknown')} 发送邮件失败: {str(e)}")
    
    def _generate_subject(self) -> str:
        """生成邮件主题"""
        today = datetime.now().strftime('%Y-%m-%d')
        return self.subject_template.format(date=today)
    
    def _generate_html_content(self, summaries: List[PaperSummary], recipient_name: str) -> str:
        """生成HTML邮件内容"""
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文摘要</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .paper { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .title { font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px; }
        .authors { color: #6b7280; margin-bottom: 10px; }
        .summary { margin-bottom: 15px; }
        .key-points { background: #f3f4f6; padding: 15px; border-radius: 6px; margin-bottom: 15px; }
        .key-points h4 { margin-top: 0; color: #374151; }
        .key-points ul { margin: 0; padding-left: 20px; }
        .metadata { font-size: 12px; color: #9ca3af; border-top: 1px solid #e5e7eb; padding-top: 10px; }
        .footer { text-align: center; color: #6b7280; font-size: 12px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; }
        a { color: #2563eb; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 今日论文摘要</h1>
        <p>亲爱的 {{ recipient_name }}，以下是为您精选的最新学术论文摘要</p>
    </div>
    
    {% for summary in summaries %}
    <div class="paper">
        <div class="title">{{ summary.paper.title }}</div>
        <div class="authors">作者: {{ summary.paper.authors | join(', ') }}</div>
        
        <div class="summary">
            <strong>AI摘要:</strong><br>
            {{ summary.summary }}
        </div>
        
        {% if summary.key_points %}
        <div class="key-points">
            <h4>关键要点:</h4>
            <ul>
            {% for point in summary.key_points %}
                <li>{{ point }}</li>
            {% endfor %}
            </ul>
        </div>
        {% endif %}
        
        <div class="metadata">
            发布时间: {{ summary.paper.published_date.strftime('%Y-%m-%d') }} | 
            分类: {{ summary.paper.categories | join(', ') }} | 
            来源: {{ summary.paper.source }}
            {% if summary.paper.pdf_url %}
            | <a href="{{ summary.paper.pdf_url }}" target="_blank">查看PDF</a>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    
    <div class="footer">
        <p>本邮件由论文自动化处理系统自动生成</p>
        <p>生成时间: {{ generated_at }}</p>
    </div>
</body>
</html>
"""
        
        template = self.jinja_env.from_string(html_template)
        return template.render(
            summaries=summaries,
            recipient_name=recipient_name,
            generated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def _generate_text_content(self, summaries: List[PaperSummary], recipient_name: str) -> str:
        """生成纯文本邮件内容"""
        lines = [
            f"📚 今日论文摘要",
            f"",
            f"亲爱的 {recipient_name}，以下是为您精选的最新学术论文摘要",
            f"",
            "=" * 60,
            ""
        ]
        
        for i, summary in enumerate(summaries, 1):
            lines.extend([
                f"{i}. {summary.paper.title}",
                f"作者: {', '.join(summary.paper.authors)}",
                f"",
                f"AI摘要:",
                summary.summary,
                f"",
            ])
            
            if summary.key_points:
                lines.append("关键要点:")
                for point in summary.key_points:
                    lines.append(f"• {point}")
                lines.append("")
            
            lines.extend([
                f"发布时间: {summary.paper.published_date.strftime('%Y-%m-%d')}",
                f"分类: {', '.join(summary.paper.categories)}",
                f"来源: {summary.paper.source}",
            ])
            
            if summary.paper.pdf_url:
                lines.append(f"PDF链接: {summary.paper.pdf_url}")
            
            lines.extend(["", "-" * 60, ""])
        
        lines.extend([
            "",
            "本邮件由论文自动化处理系统自动生成",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ])
        
        return "\n".join(lines)
    
    async def _send_email(self, to_email: str, to_name: str, subject: str, html_content: str, text_content: str):
        """发送邮件"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.username}>"
            msg['To'] = f"{to_name} <{to_email}>"
            
            # 添加文本和HTML内容
            text_part = MIMEText(text_content, 'plain', 'utf-8')
            html_part = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # 连接SMTP服务器并发送
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.server, self.port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                server.send_message(msg)
            
            logger.debug(f"邮件发送成功: {to_email}")
            
        except Exception as e:
            logger.error(f"SMTP发送失败: {str(e)}")
            raise
    
    async def test_connection(self) -> bool:
        """测试SMTP连接"""
        try:
            logger.info("开始测试SMTP连接")
            
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.server, self.port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                logger.info("SMTP连接测试成功")
                return True
                
        except Exception as e:
            logger.error(f"SMTP连接测试失败: {str(e)}")
            return False
    
    async def send_test_email(self, test_email: str):
        """发送测试邮件"""
        try:
            subject = "论文自动化系统 - 测试邮件"
            html_content = """
            <html>
            <body>
                <h2>测试邮件</h2>
                <p>这是一封来自论文自动化处理系统的测试邮件。</p>
                <p>如果您收到这封邮件，说明邮件配置正常。</p>
                <p>发送时间: {}</p>
            </body>
            </html>
            """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            text_content = f"""
            测试邮件
            
            这是一封来自论文自动化处理系统的测试邮件。
            如果您收到这封邮件，说明邮件配置正常。
            
            发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await self._send_email(test_email, "测试用户", subject, html_content, text_content)
            logger.info(f"测试邮件已发送到: {test_email}")
            
        except Exception as e:
            logger.error(f"测试邮件发送失败: {str(e)}")
            raise
