{% extends "base.html" %}

{% block title %}{{ summary.paper.title }} - {{ site.title }}{% endblock %}

{% block description %}{{ summary.summary[:150] }}...{% endblock %}

{% block extra_head %}
<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ summary.paper.title }}">
<meta property="og:description" content="{{ summary.summary[:150] }}...">
<meta property="og:type" content="article">
<meta property="og:url" content="{{ site.url }}/papers/{{ summary.paper.id }}.html">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary">
<meta name="twitter:title" content="{{ summary.paper.title }}">
<meta name="twitter:description" content="{{ summary.summary[:150] }}...">
{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">首页</a></li>
        <li class="breadcrumb-item active">论文详情</li>
    </ol>
</nav>

<div class="row">
    <div class="col-lg-8">
        <!-- 论文详情卡片 -->
        <div class="card paper-card">
            <div class="card-body">
                <!-- 论文标题 -->
                <h1 class="card-title h3 mb-3">{{ summary.paper.title }}</h1>
                
                <!-- 作者信息 -->
                <div class="mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-users me-2"></i>作者
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        {% for author in summary.paper.authors %}
                        <span class="badge bg-light text-dark">{{ author }}</span>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- 元信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-calendar me-2"></i>发布时间
                        </h6>
                        <p>{{ summary.paper.published_date.strftime('%Y年%m月%d日') }}</p>
                        
                        {% if summary.paper.updated_date %}
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-edit me-2"></i>更新时间
                        </h6>
                        <p>{{ summary.paper.updated_date.strftime('%Y年%m月%d日') }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-source me-2"></i>来源
                        </h6>
                        <p>{{ summary.paper.source }}</p>
                        
                        {% if summary.paper.doi %}
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-link me-2"></i>DOI
                        </h6>
                        <p><a href="https://doi.org/{{ summary.paper.doi }}" target="_blank">{{ summary.paper.doi }}</a></p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- 分类标签 -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-tags me-2"></i>分类
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        {% for category in summary.paper.categories %}
                        <a href="/categories/{{ category }}.html" class="category-badge">
                            {{ category }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- 原始摘要 -->
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-file-alt me-2"></i>原始摘要
                    </h5>
                    <div class="border-start border-primary border-3 ps-3">
                        <p class="text-muted">{{ summary.paper.abstract }}</p>
                    </div>
                </div>
                
                <!-- AI生成摘要 -->
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-robot me-2"></i>AI生成摘要
                    </h5>
                    <div class="alert alert-info">
                        <div class="markdown-content">{{ summary.summary | markdown | safe }}</div>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            生成时间: {{ summary.generated_at[:19] }} |
                            模型: {{ summary.model_used }}
                        </small>
                    </div>
                </div>
                
                <!-- 关键要点 -->
                {% if summary.key_points %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-lightbulb me-2"></i>关键要点
                    </h5>
                    <div class="key-points">
                        <ul>
                            {% for point in summary.key_points %}
                            <li>{{ point }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
                
                <!-- 研究方法 -->
                {% if summary.methodology %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-cogs me-2"></i>研究方法
                    </h5>
                    <div class="markdown-content">{{ summary.methodology | markdown | safe }}</div>
                </div>
                {% endif %}

                <!-- 学术意义 -->
                {% if summary.significance %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-star me-2"></i>学术意义
                    </h5>
                    <div class="markdown-content">{{ summary.significance | markdown | safe }}</div>
                </div>
                {% endif %}

                <!-- 局限性 -->
                {% if summary.limitations %}
                <div class="mb-4">
                    <h5>
                        <i class="fas fa-exclamation-triangle me-2"></i>局限性
                    </h5>
                    <div class="markdown-content">{{ summary.limitations | markdown | safe }}</div>
                </div>
                {% endif %}
                
                <!-- 操作按钮 -->
                <div class="d-flex gap-2 mb-3">
                    {% if summary.paper.pdf_url %}
                    <a href="{{ summary.paper.pdf_url }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>查看PDF
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-primary" onclick="sharePaper()">
                        <i class="fas fa-share me-2"></i>分享
                    </button>
                    
                    <button class="btn btn-outline-secondary" onclick="copyLink()">
                        <i class="fas fa-link me-2"></i>复制链接
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 快速信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>快速信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h5 mb-0">{{ summary.paper.authors|length }}</div>
                            <small class="text-muted">作者数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0">{{ summary.paper.categories|length }}</div>
                        <small class="text-muted">分类数</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相关分类 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tags me-2"></i>相关分类
                </h6>
            </div>
            <div class="card-body">
                {% for category in summary.paper.categories %}
                <a href="/categories/{{ category }}.html" class="d-block mb-2 text-decoration-none">
                    <i class="fas fa-folder me-2"></i>{{ category }}
                </a>
                {% endfor %}
            </div>
        </div>
        
        <!-- 论文统计 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>论文统计
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">摘要长度</small>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar" style="width: {{ (summary.paper.abstract|length / 2000 * 100)|round }}%"></div>
                    </div>
                    <small class="text-muted">{{ summary.paper.abstract|length }} 字符</small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">AI摘要长度</small>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: {{ (summary.summary|length / 1000 * 100)|round }}%"></div>
                    </div>
                    <small class="text-muted">{{ summary.summary|length }} 字符</small>
                </div>
                
                {% if summary.key_points %}
                <div>
                    <small class="text-muted">关键要点</small>
                    <div class="h6 mb-0">{{ summary.key_points|length }} 个</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 分享功能
function sharePaper() {
    if (navigator.share) {
        navigator.share({
            title: '{{ summary.paper.title }}',
            text: '{{ summary.summary[:100] }}...',
            url: window.location.href
        });
    } else {
        // 降级到复制链接
        copyLink();
    }
}

// 复制链接
function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>链接已复制到剪贴板
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(() => {
        alert('复制失败，请手动复制链接');
    });
}

// 添加阅读进度条
const progressBar = document.createElement('div');
progressBar.id = 'reading-progress';
progressBar.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--primary-color);
    z-index: 9999;
    transition: width 0.3s ease;
`;
document.body.appendChild(progressBar);
</script>
{% endblock %}
