2025-06-08 17:22:48 | INFO | src.utils.logger:setup_logger:68 | 日志系统初始化完成，日志级别: INFO
2025-06-08 17:22:48 | INFO | src.utils.logger:setup_logger:69 | 日志文件: logs/app.log
2025-06-08 17:22:48 | INFO | src.utils.database:init_database:76 | 数据库初始化完成
2025-06-08 17:22:48 | INFO | src.crawler.paper_crawler:__init__:295 | arXiv爬虫已启用
2025-06-08 17:22:49 | INFO | src.summarizer.ai_summarizer:__init__:546 | DeepSeek摘要生成器已启用
2025-06-08 17:22:49 | INFO | src.main:run_full_pipeline:46 | 开始执行论文自动化处理流水线
2025-06-08 17:22:49 | INFO | src.main:run_full_pipeline:49 | 步骤 1: 爬取最新论文
2025-06-08 17:22:49 | INFO | src.crawler.paper_crawler:crawl_papers:315 | 启动 arxiv 爬虫
2025-06-08 17:22:49 | INFO | src.crawler.paper_crawler:crawl:102 | 开始爬取arXiv论文: http://export.arxiv.org/api/query?search_query=%28cat%3Acs.AI+OR+cat%3Acs.LG+OR+cat%3Acs.CL+OR+cat%3Acs.CV%29+AND+submittedDate%3A%5B202505090000+TO+202506082359%5D&start=0&max_results=5&sortBy=submittedDate&sortOrder=descending
2025-06-08 17:22:49 | INFO | src.crawler.paper_crawler:crawl:103 | 查询条件: (cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506082359]
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl:109 | API响应长度: 13150 字符
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl:112 | API响应开头: <?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <link href="http://arxiv.org/api/query?search_query%3D%28cat%3Acs.AI%20OR%20cat%3Acs.LG%20OR%20cat%3Acs.CL%20OR%20cat%3Acs.CV%29%20AND%20submittedDate%3A%5B202505090000%20TO%20202506082359%5D%26id_list%3D%26start%3D0%26max_results%3D5" rel="self" type="application/atom+xml"/>
  <title type="html">ArXiv Query: search_query=(cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl:116 | 响应中包含 5 个<entry>标签
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:170 | 开始解析XML响应，长度: 13150 字符
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:172 | XML根元素: {http://www.w3.org/2005/Atom}feed
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:181 | 找到 5 个entry元素
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 1 个entry
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 1 基本元素检查:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05349v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - VideoMathQA: Benchmarking Math...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1876 chars
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 1 数据验证:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 89
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1873
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05349v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 1: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 2 个entry
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 2 基本元素检查:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05350v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Contrastive Flow Matching...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1196 chars
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 2 数据验证:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 25
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1193
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Contrastive Flow Matching...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05350v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 2: Contrastive Flow Matching...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 3 个entry
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 3 基本元素检查:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05348v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - FreeTimeGS: Free Gaussians at ...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1020 chars
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:57Z
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 3 数据验证:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 85
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1017
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05348v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 3: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 4 个entry
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 4 基本元素检查:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05344v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - SparseMM: Head Sparsity Emerge...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1373 chars
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 4 数据验证:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 70
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1370
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05344v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 4: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 5 个entry
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 5 基本元素检查:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05345v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Inference-Time Hyper-Scaling w...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1339 chars
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 5 数据验证:
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 54
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1336
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05345v1
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 5: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl:139 | 成功爬取 5 篇arXiv论文
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl_papers:331 | arxiv 爬虫完成，获取 5 篇论文
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:_apply_filters:358 | 过滤后剩余 5 篇论文
2025-06-08 17:22:54 | INFO | src.crawler.paper_crawler:crawl_papers:339 | 爬取完成，共获取 5 篇有效论文
2025-06-08 17:22:54 | INFO | src.main:run_full_pipeline:51 | 成功爬取 5 篇论文
2025-06-08 17:22:54 | INFO | src.main:run_full_pipeline:58 | 步骤 1.1: 保存论文到数据库
2025-06-08 17:22:54 | INFO | src.utils.database:save_papers:141 | 批量保存论文完成: 5/5
2025-06-08 17:22:54 | INFO | src.main:run_full_pipeline:60 | 成功保存 5 篇论文到数据库
2025-06-08 17:22:54 | INFO | src.main:run_full_pipeline:63 | 步骤 2: 生成AI摘要
2025-06-08 17:28:39 | INFO | src.summarizer.ai_summarizer:generate_summaries:587 | 成功生成 5 篇论文摘要
2025-06-08 17:28:39 | INFO | src.main:run_full_pipeline:65 | 成功生成 5 篇论文摘要
2025-06-08 17:28:39 | INFO | src.main:run_full_pipeline:68 | 步骤 2.1: 保存摘要到数据库
2025-06-08 17:28:39 | INFO | src.main:run_full_pipeline:73 | 步骤 3: 渲染网页
2025-06-08 17:28:39 | INFO | src.renderer.web_renderer:render_website:53 | 开始渲染网站
2025-06-08 17:28:39 | ERROR | src.renderer.web_renderer:_render_categories:178 | 分类页面渲染失败: 'categories' is undefined
2025-06-08 17:28:39 | ERROR | src.renderer.web_renderer:render_website:80 | 网站渲染失败: 'categories' is undefined
2025-06-08 17:28:39 | ERROR | src.main:run_full_pipeline:86 | 流水线执行失败: 'categories' is undefined
2025-06-08 17:39:05 | INFO | src.utils.logger:setup_logger:68 | 日志系统初始化完成，日志级别: INFO
2025-06-08 17:39:05 | INFO | src.utils.logger:setup_logger:69 | 日志文件: logs/app.log
2025-06-08 17:39:05 | INFO | src.utils.database:init_database:76 | 数据库初始化完成
2025-06-08 17:39:05 | INFO | src.crawler.paper_crawler:__init__:295 | arXiv爬虫已启用
2025-06-08 17:39:06 | INFO | src.summarizer.ai_summarizer:__init__:546 | DeepSeek摘要生成器已启用
2025-06-08 17:39:06 | INFO | src.renderer.web_renderer:render_website:53 | 开始渲染网站
2025-06-08 17:39:06 | WARNING | src.renderer.web_renderer:_copy_static_files:233 | 静态文件目录不存在: templates\static
2025-06-08 17:39:06 | INFO | src.renderer.web_renderer:render_website:77 | 网站渲染完成，输出目录: output
2025-06-08 17:54:17 | INFO | src.utils.logger:setup_logger:68 | 日志系统初始化完成，日志级别: INFO
2025-06-08 17:54:17 | INFO | src.utils.logger:setup_logger:69 | 日志文件: logs/app.log
2025-06-08 17:54:17 | INFO | src.utils.database:init_database:76 | 数据库初始化完成
2025-06-08 17:54:17 | INFO | src.crawler.paper_crawler:__init__:295 | arXiv爬虫已启用
2025-06-08 17:54:18 | INFO | src.summarizer.ai_summarizer:__init__:545 | DeepSeek摘要生成器已启用
2025-06-08 17:54:18 | INFO | src.renderer.web_renderer:render_website:128 | 开始渲染网站
2025-06-08 17:54:18 | WARNING | src.renderer.web_renderer:_copy_static_files:308 | 静态文件目录不存在: templates\static
2025-06-08 17:54:18 | INFO | src.renderer.web_renderer:render_website:152 | 网站渲染完成，输出目录: output
2025-06-08 17:57:32 | INFO | src.utils.logger:setup_logger:68 | 日志系统初始化完成，日志级别: INFO
2025-06-08 17:57:32 | INFO | src.utils.logger:setup_logger:69 | 日志文件: logs/app.log
2025-06-08 17:57:32 | INFO | src.utils.database:init_database:76 | 数据库初始化完成
2025-06-08 17:57:32 | INFO | src.crawler.paper_crawler:__init__:295 | arXiv爬虫已启用
2025-06-08 17:57:34 | INFO | src.summarizer.ai_summarizer:__init__:545 | DeepSeek摘要生成器已启用
2025-06-08 17:57:34 | INFO | src.main:run_full_pipeline:46 | 开始执行论文自动化处理流水线
2025-06-08 17:57:34 | INFO | src.main:run_full_pipeline:49 | 步骤 1: 爬取最新论文
2025-06-08 17:57:34 | INFO | src.crawler.paper_crawler:crawl_papers:315 | 启动 arxiv 爬虫
2025-06-08 17:57:34 | INFO | src.crawler.paper_crawler:crawl:102 | 开始爬取arXiv论文: http://export.arxiv.org/api/query?search_query=%28cat%3Acs.AI+OR+cat%3Acs.LG+OR+cat%3Acs.CL+OR+cat%3Acs.CV%29+AND+submittedDate%3A%5B202505090000+TO+202506082359%5D&start=0&max_results=5&sortBy=submittedDate&sortOrder=descending
2025-06-08 17:57:34 | INFO | src.crawler.paper_crawler:crawl:103 | 查询条件: (cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506082359]
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl:109 | API响应长度: 13150 字符
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl:112 | API响应开头: <?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <link href="http://arxiv.org/api/query?search_query%3D%28cat%3Acs.AI%20OR%20cat%3Acs.LG%20OR%20cat%3Acs.CL%20OR%20cat%3Acs.CV%29%20AND%20submittedDate%3A%5B202505090000%20TO%20202506082359%5D%26id_list%3D%26start%3D0%26max_results%3D5" rel="self" type="application/atom+xml"/>
  <title type="html">ArXiv Query: search_query=(cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl:116 | 响应中包含 5 个<entry>标签
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:170 | 开始解析XML响应，长度: 13150 字符
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:172 | XML根元素: {http://www.w3.org/2005/Atom}feed
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:181 | 找到 5 个entry元素
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 1 个entry
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 1 基本元素检查:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05349v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - VideoMathQA: Benchmarking Math...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1876 chars
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 1 数据验证:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 89
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1873
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05349v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 1: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 2 个entry
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 2 基本元素检查:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05350v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Contrastive Flow Matching...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1196 chars
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 2 数据验证:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 25
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1193
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Contrastive Flow Matching...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05350v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 2: Contrastive Flow Matching...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 3 个entry
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 3 基本元素检查:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05348v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - FreeTimeGS: Free Gaussians at ...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1020 chars
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:57Z
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 3 数据验证:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 85
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1017
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05348v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 3: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 4 个entry
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 4 基本元素检查:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05344v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - SparseMM: Head Sparsity Emerge...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1373 chars
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 4 数据验证:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 70
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1370
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05344v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 4: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 5 个entry
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 5 基本元素检查:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05345v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Inference-Time Hyper-Scaling w...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1339 chars
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 5 数据验证:
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 54
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1336
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05345v1
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 5: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl:139 | 成功爬取 5 篇arXiv论文
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl_papers:331 | arxiv 爬虫完成，获取 5 篇论文
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:_apply_filters:358 | 过滤后剩余 5 篇论文
2025-06-08 17:57:36 | INFO | src.crawler.paper_crawler:crawl_papers:339 | 爬取完成，共获取 5 篇有效论文
2025-06-08 17:57:36 | INFO | src.main:run_full_pipeline:51 | 成功爬取 5 篇论文
2025-06-08 17:57:36 | INFO | src.main:run_full_pipeline:58 | 步骤 1.1: 保存论文到数据库
2025-06-08 17:57:36 | INFO | src.utils.database:save_papers:141 | 批量保存论文完成: 5/5
2025-06-08 17:57:36 | INFO | src.main:run_full_pipeline:60 | 成功保存 5 篇论文到数据库
2025-06-08 17:57:36 | INFO | src.main:run_full_pipeline:63 | 步骤 2: 生成AI摘要
2025-06-08 18:10:38 | ERROR | src.summarizer.ai_summarizer:generate_summary:355 | DeepSeek摘要生成失败: Request timed out.
2025-06-08 18:10:38 | ERROR | src.summarizer.ai_summarizer:_generate_single_summary:623 | 论文 SparseMM: Head Sparsity Emerges from Visual Concept Responses in MLLMs 摘要生成失败: Request timed out.
2025-06-08 18:12:16 | ERROR | src.summarizer.ai_summarizer:generate_summaries:582 | 论文 SparseMM: Head Sparsity Emerges from Visual Concept Responses in MLLMs 摘要生成失败: Request timed out.
2025-06-08 18:12:16 | INFO | src.summarizer.ai_summarizer:generate_summaries:586 | 成功生成 4 篇论文摘要
2025-06-08 18:12:16 | INFO | src.main:run_full_pipeline:65 | 成功生成 4 篇论文摘要
2025-06-08 18:12:16 | INFO | src.main:run_full_pipeline:68 | 步骤 2.1: 保存摘要到数据库
2025-06-08 18:12:16 | INFO | src.main:run_full_pipeline:73 | 步骤 3: 渲染网页
2025-06-08 18:12:16 | INFO | src.renderer.web_renderer:render_website:128 | 开始渲染网站
2025-06-08 18:12:16 | WARNING | src.renderer.web_renderer:_copy_static_files:308 | 静态文件目录不存在: templates\static
2025-06-08 18:12:16 | INFO | src.renderer.web_renderer:render_website:152 | 网站渲染完成，输出目录: output
2025-06-08 18:12:16 | INFO | src.main:run_full_pipeline:75 | 网页渲染完成
2025-06-08 18:12:16 | INFO | src.main:run_full_pipeline:79 | 步骤 4: 发送邮件通知
2025-06-08 18:12:16 | INFO | src.mailer.email_sender:send_daily_digest:64 | 开始发送每日摘要邮件，共 4 篇论文
2025-06-08 18:12:21 | ERROR | src.mailer.email_sender:_send_email:276 | SMTP发送失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d9443c01a7336-23603cc3a4esm36869935ad.168 - gsmtp', '')
2025-06-08 18:12:21 | ERROR | src.mailer.email_sender:_send_to_recipient:114 | 向 <EMAIL> 发送邮件失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d9443c01a7336-23603cc3a4esm36869935ad.168 - gsmtp', '')
2025-06-08 18:12:26 | ERROR | src.mailer.email_sender:_send_email:276 | SMTP发送失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d2e1a72fcca58-7482b083ae1sm3860083b3a.74 - gsmtp', '')
2025-06-08 18:12:26 | ERROR | src.mailer.email_sender:_send_to_recipient:114 | 向 <EMAIL> 发送邮件失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d2e1a72fcca58-7482b083ae1sm3860083b3a.74 - gsmtp', '')
2025-06-08 18:12:26 | INFO | src.mailer.email_sender:send_daily_digest:70 | 每日摘要邮件发送完成
2025-06-08 18:12:26 | INFO | src.main:run_full_pipeline:81 | 邮件发送完成
2025-06-08 18:12:26 | INFO | src.main:run_full_pipeline:83 | 论文自动化处理流水线执行完成
2025-06-08 21:09:14 | INFO | src.utils.logger:setup_logger:68 | 日志系统初始化完成，日志级别: INFO
2025-06-08 21:09:14 | INFO | src.utils.logger:setup_logger:69 | 日志文件: logs/app.log
2025-06-08 21:09:14 | INFO | src.utils.database:init_database:76 | 数据库初始化完成
2025-06-08 21:09:14 | INFO | src.crawler.paper_crawler:__init__:295 | arXiv爬虫已启用
2025-06-08 21:09:15 | INFO | src.summarizer.ai_summarizer:__init__:545 | DeepSeek摘要生成器已启用
2025-06-08 21:09:15 | INFO | src.main:run_full_pipeline:46 | 开始执行论文自动化处理流水线
2025-06-08 21:09:15 | INFO | src.main:run_full_pipeline:49 | 步骤 1: 爬取最新论文
2025-06-08 21:09:15 | INFO | src.crawler.paper_crawler:crawl_papers:315 | 启动 arxiv 爬虫
2025-06-08 21:09:15 | INFO | src.crawler.paper_crawler:crawl:102 | 开始爬取arXiv论文: http://export.arxiv.org/api/query?search_query=%28cat%3Acs.AI+OR+cat%3Acs.LG+OR+cat%3Acs.CL+OR+cat%3Acs.CV%29+AND+submittedDate%3A%5B202505090000+TO+202506082359%5D&start=0&max_results=5&sortBy=submittedDate&sortOrder=descending
2025-06-08 21:09:15 | INFO | src.crawler.paper_crawler:crawl:103 | 查询条件: (cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506082359]
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl:109 | API响应长度: 13150 字符
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl:112 | API响应开头: <?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <link href="http://arxiv.org/api/query?search_query%3D%28cat%3Acs.AI%20OR%20cat%3Acs.LG%20OR%20cat%3Acs.CL%20OR%20cat%3Acs.CV%29%20AND%20submittedDate%3A%5B202505090000%20TO%20202506082359%5D%26id_list%3D%26start%3D0%26max_results%3D5" rel="self" type="application/atom+xml"/>
  <title type="html">ArXiv Query: search_query=(cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV) AND submittedDate:[202505090000 TO 202506
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl:116 | 响应中包含 5 个<entry>标签
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:170 | 开始解析XML响应，长度: 13150 字符
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:172 | XML根元素: {http://www.w3.org/2005/Atom}feed
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:181 | 找到 5 个entry元素
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 1 个entry
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 1 基本元素检查:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05349v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - VideoMathQA: Benchmarking Math...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1876 chars
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 1 数据验证:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 89
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1873
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05349v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 1: VideoMathQA: Benchmarking Mathematical Reasoning v...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 2 个entry
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 2 基本元素检查:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05350v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Contrastive Flow Matching...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1196 chars
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:58Z
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 2 数据验证:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 25
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1193
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Contrastive Flow Matching...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05350v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 2: Contrastive Flow Matching...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 3 个entry
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 3 基本元素检查:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05348v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - FreeTimeGS: Free Gaussians at ...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1020 chars
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:57Z
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 3 数据验证:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 85
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1017
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05348v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 3: FreeTimeGS: Free Gaussians at Anytime and Anywhere...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 4 个entry
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 4 基本元素检查:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05344v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - SparseMM: Head Sparsity Emerge...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1373 chars
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 4 数据验证:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 70
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1370
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05344v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 4: SparseMM: Head Sparsity Emerges from Visual Concep...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:184 | 处理第 5 个entry
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:193 | Entry 5 基本元素检查:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:194 |   - id: ✓ - http://arxiv.org/abs/2506.05345v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:195 |   - title: ✓ - Inference-Time Hyper-Scaling w...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:196 |   - summary: ✓ - 1339 chars
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:197 |   - published: ✓ - 2025-06-05T17:59:55Z
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:201 |   - all() 检查: [True, True, True, True] -> True
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:235 | Entry 5 数据验证:
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:236 |   - 标题长度: 54
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:237 |   - 摘要长度: 1336
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:238 |   - 标题: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:250 |   - arXiv ID: 2506.05345v1
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_parse_arxiv_response:270 | ✅ 成功解析论文 5: Inference-Time Hyper-Scaling with KV Cache Compres...
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl:139 | 成功爬取 5 篇arXiv论文
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl_papers:331 | arxiv 爬虫完成，获取 5 篇论文
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:_apply_filters:358 | 过滤后剩余 5 篇论文
2025-06-08 21:09:25 | INFO | src.crawler.paper_crawler:crawl_papers:339 | 爬取完成，共获取 5 篇有效论文
2025-06-08 21:09:25 | INFO | src.main:run_full_pipeline:51 | 成功爬取 5 篇论文
2025-06-08 21:09:25 | INFO | src.main:run_full_pipeline:58 | 步骤 1.1: 保存论文到数据库
2025-06-08 21:09:25 | INFO | src.utils.database:save_papers:141 | 批量保存论文完成: 5/5
2025-06-08 21:09:25 | INFO | src.main:run_full_pipeline:60 | 成功保存 5 篇论文到数据库
2025-06-08 21:09:25 | INFO | src.main:run_full_pipeline:63 | 步骤 2: 生成AI摘要
2025-06-08 21:24:45 | INFO | src.summarizer.ai_summarizer:generate_summaries:586 | 成功生成 5 篇论文摘要
2025-06-08 21:24:45 | INFO | src.main:run_full_pipeline:65 | 成功生成 5 篇论文摘要
2025-06-08 21:24:45 | INFO | src.main:run_full_pipeline:68 | 步骤 2.1: 保存摘要到数据库
2025-06-08 21:24:45 | INFO | src.main:run_full_pipeline:73 | 步骤 3: 渲染网页
2025-06-08 21:24:45 | INFO | src.renderer.web_renderer:render_website:128 | 开始渲染网站
2025-06-08 21:24:45 | WARNING | src.renderer.web_renderer:_copy_static_files:308 | 静态文件目录不存在: templates\static
2025-06-08 21:24:45 | INFO | src.renderer.web_renderer:render_website:152 | 网站渲染完成，输出目录: output
2025-06-08 21:24:45 | INFO | src.main:run_full_pipeline:75 | 网页渲染完成
2025-06-08 21:24:45 | INFO | src.main:run_full_pipeline:79 | 步骤 4: 发送邮件通知
2025-06-08 21:24:45 | INFO | src.mailer.email_sender:send_daily_digest:64 | 开始发送每日摘要邮件，共 5 篇论文
2025-06-08 21:24:50 | ERROR | src.mailer.email_sender:_send_email:276 | SMTP发送失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. 98e67ed59e1d1-3134b1551c1sm3991094a91.45 - gsmtp', '')
2025-06-08 21:24:50 | ERROR | src.mailer.email_sender:_send_to_recipient:114 | 向 <EMAIL> 发送邮件失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. 98e67ed59e1d1-3134b1551c1sm3991094a91.45 - gsmtp', '')
2025-06-08 21:24:55 | ERROR | src.mailer.email_sender:_send_email:276 | SMTP发送失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d9443c01a7336-23603c6c325sm38295365ad.173 - gsmtp', '')
2025-06-08 21:24:55 | ERROR | src.mailer.email_sender:_send_to_recipient:114 | 向 <EMAIL> 发送邮件失败: (530, b'5.7.0 Authentication Required. For more information, go to\n5.7.0  https://support.google.com/accounts/troubleshooter/2402620. d9443c01a7336-23603c6c325sm38295365ad.173 - gsmtp', '')
2025-06-08 21:24:55 | INFO | src.mailer.email_sender:send_daily_digest:70 | 每日摘要邮件发送完成
2025-06-08 21:24:55 | INFO | src.main:run_full_pipeline:81 | 邮件发送完成
2025-06-08 21:24:55 | INFO | src.main:run_full_pipeline:83 | 论文自动化处理流水线执行完成
