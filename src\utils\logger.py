"""
日志配置模块
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(config_manager=None) -> logger:
    """
    设置日志配置
    
    Args:
        config_manager: 配置管理器实例
        
    Returns:
        配置好的logger实例
    """
    # 移除默认的handler
    logger.remove()
    
    # 从配置获取日志设置
    if config_manager:
        log_level = config_manager.get('logging.level', 'INFO')
        log_format = config_manager.get(
            'logging.format', 
            "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        )
        log_file = config_manager.get('logging.file', 'logs/app.log')
        rotation = config_manager.get('logging.rotation', '1 week')
        retention = config_manager.get('logging.retention', '1 month')
    else:
        log_level = 'INFO'
        log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        log_file = 'logs/app.log'
        rotation = '1 week'
        retention = '1 month'
    
    # 确保日志目录存在
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件输出
    logger.add(
        log_file,
        format=log_format,
        level=log_level,
        rotation=rotation,
        retention=retention,
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    logger.info(f"日志系统初始化完成，日志级别: {log_level}")
    logger.info(f"日志文件: {log_file}")
    
    return logger


def get_module_logger(module_name: str):
    """
    获取模块专用的logger
    
    Args:
        module_name: 模块名称
        
    Returns:
        logger实例
    """
    return logger.bind(name=module_name)
