"""
网页渲染模块
"""

import json
import shutil
import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from jinja2 import Environment, FileSystemLoader
import markdown
from markdown.extensions import codehilite, toc, tables, fenced_code

from src.summarizer.ai_summarizer import PaperSummary
from src.utils.logger import get_module_logger

logger = get_module_logger("renderer")


class WebRenderer:
    """网页渲染器"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.renderer_config = self.config.get_section('renderer')
        
        # 配置路径
        self.output_dir = Path(self.renderer_config.get('output', {}).get('directory', 'output'))
        self.template_dir = Path(self.renderer_config.get('output', {}).get('template_dir', 'templates'))
        self.static_dir = self.output_dir / 'static'
        
        # 确保目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.static_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )

        # 添加自定义过滤器
        self.jinja_env.filters['markdown'] = self._markdown_filter

        # 初始化Markdown处理器
        self.markdown_processor = markdown.Markdown(
            extensions=[
                'markdown.extensions.fenced_code',
                'markdown.extensions.codehilite',
                'markdown.extensions.tables',
                'markdown.extensions.toc',
                'markdown.extensions.nl2br'
            ],
            extension_configs={
                'markdown.extensions.codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': True
                },
                'markdown.extensions.toc': {
                    'permalink': True
                }
            }
        )

        # 网站配置
        self.site_config = self.renderer_config.get('site', {})
        self.page_config = self.renderer_config.get('pages', {})
        self.theme_config = self.renderer_config.get('theme', {})

    def _markdown_filter(self, text):
        """Jinja2 Markdown过滤器"""
        if not text:
            return ""

        # 重置markdown处理器状态
        self.markdown_processor.reset()

        # 处理LaTeX数学公式，避免被markdown处理
        # 保护行内公式 $...$
        inline_math_pattern = r'\$([^$]+)\$'
        inline_math_placeholders = {}
        inline_counter = 0

        def replace_inline_math(match):
            nonlocal inline_counter
            placeholder = f"__INLINE_MATH_{inline_counter}__"
            inline_math_placeholders[placeholder] = f"${match.group(1)}$"
            inline_counter += 1
            return placeholder

        text = re.sub(inline_math_pattern, replace_inline_math, text)

        # 保护块级公式 $$...$$
        block_math_pattern = r'\$\$([^$]+)\$\$'
        block_math_placeholders = {}
        block_counter = 0

        def replace_block_math(match):
            nonlocal block_counter
            placeholder = f"__BLOCK_MATH_{block_counter}__"
            block_math_placeholders[placeholder] = f"$${match.group(1)}$$"
            block_counter += 1
            return placeholder

        text = re.sub(block_math_pattern, replace_block_math, text)

        # 转换markdown
        html = self.markdown_processor.convert(text)

        # 恢复数学公式
        for placeholder, formula in inline_math_placeholders.items():
            html = html.replace(placeholder, formula)

        for placeholder, formula in block_math_placeholders.items():
            html = html.replace(placeholder, formula)

        return html
    
    async def render_website(self, summaries: List[PaperSummary]):
        """
        渲染完整网站
        
        Args:
            summaries: 论文摘要列表
        """
        try:
            logger.info("开始渲染网站")
            
            # 准备数据
            data = self._prepare_data(summaries)
            
            # 渲染主页
            await self._render_index(data)
            
            # 渲染分类页面
            await self._render_categories(data)
            
            # 渲染详情页面
            await self._render_details(summaries)
            
            # 复制静态文件
            await self._copy_static_files()
            
            # 生成RSS
            if self.page_config.get('enable_rss', True):
                await self._generate_rss(summaries)
            
            # 生成sitemap
            await self._generate_sitemap(summaries)
            
            logger.info(f"网站渲染完成，输出目录: {self.output_dir}")
            
        except Exception as e:
            logger.error(f"网站渲染失败: {str(e)}")
            raise
    
    def _prepare_data(self, summaries: List[PaperSummary]) -> Dict[str, Any]:
        """准备渲染数据"""
        # 按分类分组
        categories = {}
        for summary in summaries:
            for category in summary.paper.categories:
                if category not in categories:
                    categories[category] = []
                categories[category].append(summary)
        
        # 按日期排序
        sorted_summaries = sorted(
            summaries, 
            key=lambda x: x.paper.published_date, 
            reverse=True
        )
        
        # 统计信息
        stats = {
            'total_papers': len(summaries),
            'categories_count': len(categories),
            'latest_date': sorted_summaries[0].paper.published_date if sorted_summaries else None,
            'sources': list(set(s.paper.source for s in summaries))
        }
        
        return {
            'summaries': sorted_summaries,
            'categories': categories,
            'stats': stats,
            'site': self.site_config,
            'theme': self.theme_config,
            'generated_at': datetime.now().isoformat()
        }
    
    async def _render_index(self, data: Dict[str, Any]):
        """渲染主页"""
        try:
            template = self.jinja_env.get_template('index.html')
            
            # 分页处理
            papers_per_page = self.page_config.get('papers_per_page', 20)
            summaries = data['summaries']
            
            # 渲染第一页
            page_summaries = summaries[:papers_per_page]
            html_content = template.render(
                summaries=page_summaries,
                categories=data['categories'],
                stats=data['stats'],
                site=data['site'],
                theme=data['theme'],
                current_page=1,
                total_pages=(len(summaries) + papers_per_page - 1) // papers_per_page,
                has_next=len(summaries) > papers_per_page
            )
            
            # 写入文件
            index_file = self.output_dir / 'index.html'
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.debug("主页渲染完成")
            
        except Exception as e:
            logger.error(f"主页渲染失败: {str(e)}")
            raise
    
    async def _render_categories(self, data: Dict[str, Any]):
        """渲染分类页面"""
        if not self.page_config.get('enable_categories', True):
            return
        
        try:
            template = self.jinja_env.get_template('category.html')
            
            for category, summaries in data['categories'].items():
                html_content = template.render(
                    category=category,
                    summaries=summaries,
                    categories=data['categories'],  # 添加categories变量供base.html使用
                    site=data['site'],
                    theme=data['theme']
                )
                
                # 创建分类目录
                category_dir = self.output_dir / 'categories'
                category_dir.mkdir(exist_ok=True)
                
                # 写入文件
                category_file = category_dir / f'{category}.html'
                with open(category_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
            
            logger.debug(f"分类页面渲染完成，共 {len(data['categories'])} 个分类")
            
        except Exception as e:
            logger.error(f"分类页面渲染失败: {str(e)}")
            raise
    
    async def _render_details(self, summaries: List[PaperSummary]):
        """渲染详情页面"""
        try:
            template = self.jinja_env.get_template('detail.html')

            # 创建详情目录
            detail_dir = self.output_dir / 'papers'
            detail_dir.mkdir(exist_ok=True)

            # 准备categories数据供base.html使用
            categories = {}
            for summary in summaries:
                for category in summary.paper.categories:
                    if category not in categories:
                        categories[category] = []
                    categories[category].append(summary)

            for summary in summaries:
                html_content = template.render(
                    summary=summary,
                    categories=categories,  # 添加categories变量供base.html使用
                    site=self.site_config,
                    theme=self.theme_config
                )
                
                # 使用论文ID作为文件名
                detail_file = detail_dir / f'{summary.paper.id}.html'
                with open(detail_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
            
            logger.debug(f"详情页面渲染完成，共 {len(summaries)} 篇论文")
            
        except Exception as e:
            logger.error(f"详情页面渲染失败: {str(e)}")
            raise
    
    async def _copy_static_files(self):
        """复制静态文件"""
        try:
            static_source = self.template_dir / 'static'
            if static_source.exists():
                # 复制CSS、JS等静态文件
                for item in static_source.rglob('*'):
                    if item.is_file():
                        relative_path = item.relative_to(static_source)
                        target_path = self.static_dir / relative_path
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(item, target_path)
                
                logger.debug("静态文件复制完成")
            else:
                logger.warning(f"静态文件目录不存在: {static_source}")
                
        except Exception as e:
            logger.error(f"静态文件复制失败: {str(e)}")
    
    async def _generate_rss(self, summaries: List[PaperSummary]):
        """生成RSS订阅"""
        try:
            template = self.jinja_env.get_template('rss.xml')
            
            # 只包含最新的20篇论文
            recent_summaries = sorted(
                summaries, 
                key=lambda x: x.paper.published_date, 
                reverse=True
            )[:20]
            
            rss_content = template.render(
                summaries=recent_summaries,
                site=self.site_config,
                generated_at=datetime.now().isoformat()
            )
            
            rss_file = self.output_dir / 'rss.xml'
            with open(rss_file, 'w', encoding='utf-8') as f:
                f.write(rss_content)
            
            logger.debug("RSS文件生成完成")
            
        except Exception as e:
            logger.error(f"RSS生成失败: {str(e)}")
    
    async def _generate_sitemap(self, summaries: List[PaperSummary]):
        """生成sitemap"""
        try:
            template = self.jinja_env.get_template('sitemap.xml')
            
            # 收集所有URL
            urls = [
                {'loc': '/', 'lastmod': datetime.now().isoformat(), 'priority': '1.0'},
            ]
            
            # 添加分类页面
            categories = set()
            for summary in summaries:
                categories.update(summary.paper.categories)
            
            for category in categories:
                urls.append({
                    'loc': f'/categories/{category}.html',
                    'lastmod': datetime.now().isoformat(),
                    'priority': '0.8'
                })
            
            # 添加论文详情页面
            for summary in summaries:
                urls.append({
                    'loc': f'/papers/{summary.paper.id}.html',
                    'lastmod': summary.paper.published_date.isoformat(),
                    'priority': '0.6'
                })
            
            sitemap_content = template.render(
                urls=urls,
                site_url=self.site_config.get('url', '')
            )
            
            sitemap_file = self.output_dir / 'sitemap.xml'
            with open(sitemap_file, 'w', encoding='utf-8') as f:
                f.write(sitemap_content)
            
            logger.debug("Sitemap生成完成")
            
        except Exception as e:
            logger.error(f"Sitemap生成失败: {str(e)}")
    
    async def test_render(self):
        """测试渲染功能"""
        logger.info("开始测试网页渲染")
        
        try:
            # 创建测试数据
            from src.crawler.paper_crawler import Paper
            from src.summarizer.ai_summarizer import PaperSummary
            
            test_paper = Paper(
                id="test-001",
                title="测试论文标题",
                authors=["测试作者1", "测试作者2"],
                abstract="这是一个测试论文的摘要内容。",
                published_date=datetime.now(),
                updated_date=None,
                categories=["cs.AI"],
                pdf_url="https://example.com/test.pdf",
                source="test"
            )
            
            test_summary = PaperSummary(
                paper=test_paper,
                summary="这是一个测试摘要。",
                key_points=["要点1", "要点2", "要点3"],
                methodology="测试方法",
                significance="测试意义",
                limitations="测试局限性",
                generated_at=datetime.now().isoformat(),
                model_used="test"
            )
            
            # 渲染测试页面
            await self.render_website([test_summary])
            
            logger.info("网页渲染测试完成")
            return True
            
        except Exception as e:
            logger.error(f"网页渲染测试失败: {str(e)}")
            return False
